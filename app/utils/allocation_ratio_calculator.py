"""
分摊占比计算工具类

提供统一的分摊占比计算逻辑，确保在不同场景下使用相同的计算方法。
"""
from typing import Dict, List, Tuple
from dataclasses import dataclass


@dataclass
class DepartmentRatio:
    """部门分摊比例信息"""
    department_id: str
    department_name: str
    person_count: int
    allocation_ratio: float  # 分摊比例 (0.0 - 1.0)
    percentage: float  # 百分比 (0.0 - 100.0)


class AllocationRatioCalculator:
    """分摊占比计算器"""
    
    @staticmethod
    def calculate_department_ratios(
        department_stats: Dict[str, Dict[str, any]], 
        total_people: int = None
    ) -> List[DepartmentRatio]:
        """
        计算各部门分摊比例
        
        Args:
            department_stats: 部门统计信息 {dept_id: {"count": int, "name": str}}
            total_people: 总人数，如果不提供则自动计算
            
        Returns:
            List[DepartmentRatio]: 部门分摊比例列表
        """
        if not department_stats:
            return []
        
        # 计算总人数
        if total_people is None:
            total_people = sum(stats["count"] for stats in department_stats.values())
        
        if total_people == 0:
            return []
        
        ratios = []
        for dept_id, stats in department_stats.items():
            person_count = stats["count"]
            # 计算分摊比例 = 该部门人数 / 总人数
            allocation_ratio = person_count / total_people
            percentage = allocation_ratio * 100
            
            ratios.append(DepartmentRatio(
                department_id=dept_id,
                department_name=stats["name"],
                person_count=person_count,
                allocation_ratio=allocation_ratio,
                percentage=round(percentage, 1)
            ))
        
        return ratios
    
    @staticmethod
    def calculate_department_ratios_from_residents(
        residents_by_dept: Dict[str, List[Dict]]
    ) -> List[DepartmentRatio]:
        """
        从住户列表计算各部门分摊比例
        
        Args:
            residents_by_dept: 按部门分组的住户列表 {dept_name: [resident_dict, ...]}
            
        Returns:
            List[DepartmentRatio]: 部门分摊比例列表
        """
        if not residents_by_dept:
            return []
        
        # 转换为标准格式
        department_stats = {}
        for dept_name, residents in residents_by_dept.items():
            # 使用部门名称作为ID（为了兼容现有逻辑）
            department_stats[dept_name] = {
                "count": len(residents),
                "name": dept_name
            }
        
        return AllocationRatioCalculator.calculate_department_ratios(department_stats)
    
    @staticmethod
    def calculate_percentage_from_ratio(allocation_ratio: float) -> float:
        """
        将分摊比例转换为百分比
        
        Args:
            allocation_ratio: 分摊比例 (0.0 - 1.0)
            
        Returns:
            float: 百分比 (0.0 - 100.0)，保留1位小数
        """
        return round(allocation_ratio * 100, 1)
    
    @staticmethod
    def calculate_ratio_from_percentage(percentage: float) -> float:
        """
        将百分比转换为分摊比例
        
        Args:
            percentage: 百分比 (0.0 - 100.0)
            
        Returns:
            float: 分摊比例 (0.0 - 1.0)，保留4位小数
        """
        return round(percentage / 100, 4)
