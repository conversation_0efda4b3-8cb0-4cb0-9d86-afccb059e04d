"""
报表服务
"""
from typing import List, Optional, Tuple, Dict
from datetime import date, datetime
from sqlalchemy.orm import Session

from app.models.dormitory import Dormitory
from app.models.record import ResidenceRecord
from app.repositories.report_repo import DailyAllocationRepository
from app.repositories.dormitory_repo import DormitoryRepository
from app.repositories.record_repo import RecordRepository
from app.services.allocation_calculator import AllocationCalculator
from app.schemas.report import (
    MonthlyReportResponse, DailyAllocationResponse, DailyAllocationDetail,
    MonthlyDistributionResponse, DormitoryDistributionDetail, DormitoryDistributionRecord
)
from app.core.logging import get_logger
from app.core.config import settings
from app.utils.allocation_ratio_calculator import AllocationRatioCalculator

logger = get_logger(__name__)


class ReportService:
    """报表服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.daily_repo = DailyAllocationRepository(db)
        self.dormitory_repo = DormitoryRepository(db)
        self.record_repo = RecordRepository(db)
        self.calculator = AllocationCalculator(settings.unit_cost_per_bed_day)
    
    async def get_monthly_report(
        self,
        year: int,
        month: int,
        end_date: Optional[date] = None
    ) -> MonthlyReportResponse:
        """
        获取月度报告（实时计算）

        Args:
            year: 年份
            month: 月份
            end_date: 截止日期，用于实时统计

        Returns:
            MonthlyReportResponse: 月度报告
        """
        logger.info(f"开始实时计算月度报告: {year}年{month}月, 截止日期: {end_date}")

        # 实时计算月度报告
        start_time = datetime.now()

        # 1. 获取日期范围
        date_range = self.calculator.get_month_date_range(year, month, end_date)

        # 2. 获取所有宿舍和入住记录
        dormitories = self.dormitory_repo.get_active_dormitories()
        residence_records = self.record_repo.get_records_in_date_range(
            date_range[0], date_range[-1]
        )

        # 3. 计算每日分摊
        daily_allocations = []
        for target_date in date_range:
            for dormitory in dormitories:
                daily_result = self.calculator.calculate_daily_allocation(
                    target_date, dormitory, residence_records
                )
                daily_allocations.append(daily_result)

        # 4. 计算月度汇总
        department_summary = self.calculator.calculate_monthly_summary(
            year, month, daily_allocations, end_date
        )

        # 5. 记录计算耗时
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"月度报告实时计算完成，耗时: {duration:.2f}秒")

        return MonthlyReportResponse(
            id="realtime",  # 实时计算的报告使用固定ID
            year=year,
            month=month,
            report_date=end_date or date_range[-1],
            department_summary=department_summary,
            daily_details=[self._daily_result_to_detail(d) for d in daily_allocations],
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

    async def get_monthly_report_for_export(
        self,
        year: int,
        month: int,
        end_date: Optional[date] = None
    ) -> Dict:
        """
        获取用于导出的月度报告数据（按宿舍-部门-人员结构组织）

        Args:
            year: 年份
            month: 月份
            end_date: 截止日期，用于实时统计

        Returns:
            Dict: 导出用的报告数据
        """
        logger.info(f"开始获取导出用月度报告数据: {year}年{month}月, 截止日期: {end_date}")

        # 1. 获取日期范围
        date_range = self.calculator.get_month_date_range(year, month, end_date)

        # 2. 获取所有宿舍和入住记录
        dormitories = self.dormitory_repo.get_active_dormitories()
        residence_records = self.record_repo.get_records_in_date_range(
            date_range[0], date_range[-1]
        )

        # 3. 按宿舍组织数据
        dormitory_data = []
        total_residents = 0

        for dormitory in dormitories:
            # 获取该宿舍在指定月份的入住记录
            dorm_records = [r for r in residence_records if r.dormitory_id == dormitory.id]

            if not dorm_records:
                continue

            # 按部门分组
            dept_groups = {}
            for record in dorm_records:
                dept_name = record.resident.department.name if record.resident.department else "未分配部门"
                if dept_name not in dept_groups:
                    dept_groups[dept_name] = []
                dept_groups[dept_name].append({
                    "name": record.resident.name,
                    "department": dept_name,
                    "project_group": record.project_group,
                    "bed_number": record.bed_number
                })

            # 计算部门占比 - 使用通用分摊占比计算逻辑
            total_dorm_residents = len(dorm_records)
            total_residents += total_dorm_residents

            # 使用通用计算器计算分摊占比
            department_ratios = AllocationRatioCalculator.calculate_department_ratios_from_residents(dept_groups)

            departments = []
            for ratio in department_ratios:
                # 找到对应的住户列表
                residents = dept_groups[ratio.department_name]
                departments.append({
                    "department_name": ratio.department_name,
                    "percentage": ratio.percentage,
                    "residents": sorted(residents, key=lambda x: x["bed_number"])
                })

            # 按占比排序部门
            departments.sort(key=lambda x: x["percentage"], reverse=True)

            dormitory_data.append({
                "dormitory_name": dormitory.name,
                "total_residents": total_dorm_residents,
                "departments": departments
            })

        # 按宿舍名称排序
        dormitory_data.sort(key=lambda x: x["dormitory_name"])

        return {
            "year": year,
            "month": month,
            "end_date": end_date or date_range[-1],
            "total_residents": total_residents,
            "dormitory_count": len(dormitory_data),
            "department_count": len(set(dept["department_name"] for dorm in dormitory_data for dept in dorm["departments"])),
            "dormitories": dormitory_data
        }
    
    async def get_daily_allocations(
        self,
        start_date: date,
        end_date: date,
        dormitory_id: Optional[str] = None
    ) -> List[DailyAllocationResponse]:
        """获取日度分摊明细"""
        try:
            allocations = self.daily_repo.get_by_date_range(start_date, end_date, dormitory_id)
            
            result = []
            for allocation in allocations:
                result.append(DailyAllocationResponse(
                    id=allocation.id,
                    allocation_date=allocation.allocation_date,
                    dormitory_id=allocation.dormitory_id,
                    dormitory_name=allocation.dormitory.name if allocation.dormitory else "",
                    total_beds=allocation.total_beds,
                    occupied_beds=allocation.occupied_beds,
                    department_allocations=allocation.department_allocations
                ))
            
            logger.info(f"获取日度分摊明细成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取日度分摊明细失败: {str(e)}")
            raise
    
    async def get_realtime_report(self) -> MonthlyReportResponse:
        """获取当月实时报告（截止到今天）"""
        now = datetime.now()
        return await self.get_monthly_report(now.year, now.month, now.date())

    async def get_monthly_distribution_details(
        self,
        year: int,
        month: int,
        end_date: Optional[date] = None
    ) -> MonthlyDistributionResponse:
        """
        获取月度宿舍部门分布详情数据

        Args:
            year: 年份
            month: 月份
            end_date: 截止日期，用于实时统计

        Returns:
            MonthlyDistributionResponse: 宿舍分布详情
        """
        logger.info(f"开始获取月度宿舍分布详情: {year}年{month}月, 截止日期: {end_date}")

        # 1. 获取日期范围
        date_range = self.calculator.get_month_date_range(year, month, end_date)
        start_date = date_range[0]
        actual_end_date = date_range[-1]

        # 2. 获取所有宿舍和入住记录
        dormitories = self.dormitory_repo.get_active_dormitories()
        residence_records = self.record_repo.get_records_in_date_range(start_date, actual_end_date)

        # 3. 按宿舍组织数据
        dormitory_details = []
        total_records = 0

        for dormitory in dormitories:
            # 获取该宿舍在指定月份的入住记录
            dorm_records = [r for r in residence_records if r.dormitory_id == dormitory.id]

            if not dorm_records:
                # 空宿舍也要显示
                dormitory_details.append(DormitoryDistributionDetail(
                    dormitory_name=dormitory.name,
                    total_beds=dormitory.total_beds,
                    records=[],
                    department_summary={}
                ))
                continue

            # 处理每条入住记录
            distribution_records = []
            department_summary = {}

            for record in dorm_records:
                # 计算在该月份的入住天数
                record_start = max(record.check_in_date, start_date)
                record_end = min(record.check_out_date or actual_end_date, actual_end_date)

                # 只统计在月份范围内的记录
                if record_start <= actual_end_date and record_end >= start_date:
                    days_in_month = (record_end - record_start).days + 1

                    dept_name = record.resident.department.name if record.resident.department else "未分配部门"

                    distribution_records.append(DormitoryDistributionRecord(
                        record_id=record.id,
                        resident_name=record.resident.name,
                        department_name=dept_name,
                        project_group=record.project_group or "",
                        bed_number=record.bed_number,
                        check_in_date=record.check_in_date,
                        check_out_date=record.check_out_date,
                        status=record.status,
                        days_in_month=days_in_month
                    ))

                    # 统计部门人数
                    department_summary[dept_name] = department_summary.get(dept_name, 0) + 1

            # 按床位号排序
            distribution_records.sort(key=lambda x: x.bed_number)

            dormitory_details.append(DormitoryDistributionDetail(
                dormitory_name=dormitory.name,
                total_beds=dormitory.total_beds,
                records=distribution_records,
                department_summary=department_summary
            ))

            total_records += len(distribution_records)

        # 按宿舍名称排序
        dormitory_details.sort(key=lambda x: x.dormitory_name)

        logger.info(f"月度宿舍分布详情获取完成，共{total_records}条记录")

        return MonthlyDistributionResponse(
            year=year,
            month=month,
            start_date=start_date,
            end_date=actual_end_date,
            total_records=total_records,
            dormitories=dormitory_details
        )
    

    

    
    def _daily_result_to_detail(self, daily_result) -> DailyAllocationDetail:
        """将日度结果转换为响应模型"""
        from app.schemas.report import ResidentInfo

        residents = [
            ResidentInfo(
                id=resident.id,
                name=resident.name,
                department_name=resident.department_name,
                bed_number=resident.bed_number
            )
            for resident in daily_result.residents
        ]

        return DailyAllocationDetail(
            date=daily_result.date,
            dormitory_id=daily_result.dormitory_id,
            dormitory_name=daily_result.dormitory_name,
            total_beds=daily_result.total_beds,
            occupied_beds=daily_result.occupied_beds,
            residents=residents,
            department_allocations=[
                {
                    "department_id": alloc.department_id,
                    "department_name": alloc.department_name,
                    "bed_count": alloc.bed_count,
                    "allocation_ratio": alloc.allocation_ratio
                }
                for alloc in daily_result.department_allocations
            ]
        )
