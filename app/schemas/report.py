"""
报表相关模式
"""
from typing import List, Optional, Any, Dict
from datetime import date
from pydantic import BaseModel, Field


class DepartmentSummary(BaseModel):
    """部门汇总信息"""
    department_id: str
    department_name: str
    bed_days: float
    cost: float
    ratio: float


class ResidentInfo(BaseModel):
    """住户信息"""
    id: str
    name: str
    department_name: Optional[str] = None
    bed_number: int


class DailyAllocationDetail(BaseModel):
    """日度分摊明细"""
    date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    residents: List[ResidentInfo] = []
    department_allocations: List[Dict[str, Any]] = []


class MonthlyReportResponse(BaseModel):
    """月度报告响应模式"""
    id: str
    year: int
    month: int
    report_date: date
    department_summary: List[DepartmentSummary] = []
    daily_details: List[DailyAllocationDetail] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class DailyAllocationResponse(BaseModel):
    """日度分摊响应模式"""
    id: str
    allocation_date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    department_allocations: List[Dict[str, Any]] = []


class ReportExportRequest(BaseModel):
    """报告导出请求"""
    report_type: str
    export_format: str
    year: int
    month: int
    end_date: Optional[date] = None


class DormitoryDistributionRecord(BaseModel):
    """宿舍分布记录"""
    record_id: str
    resident_name: str
    department_name: str
    project_group: str
    bed_number: int
    check_in_date: date
    check_out_date: Optional[date] = None
    status: str
    days_in_month: int  # 在该月份的入住天数


class DormitoryDistributionDetail(BaseModel):
    """宿舍分布详情"""
    dormitory_name: str
    total_beds: int
    records: List[DormitoryDistributionRecord] = []
    department_summary: Dict[str, int] = {}  # 部门名称 -> 人数


class MonthlyDistributionResponse(BaseModel):
    """月度分布响应"""
    year: int
    month: int
    start_date: date
    end_date: date
    total_records: int
    dormitories: List[DormitoryDistributionDetail] = []
