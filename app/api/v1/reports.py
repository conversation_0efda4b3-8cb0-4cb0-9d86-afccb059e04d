"""
报表统计API
"""
from datetime import date
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.auth.dependencies import get_current_user
from app.utils.export_utils import ExportUtils
from app.services.report_service import ReportService
from app.schemas.report import MonthlyDistributionResponse

router = APIRouter()


@router.get("/monthly/{year}/{month}", summary="获取月度报告")
async def get_monthly_report(
    year: int,
    month: int,
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取指定年月的费用分摊报告（实时计算）

    - **year**: 年份
    - **month**: 月份 (1-12)
    - **end_date**: 截止日期，不指定则统计整月
    """
    if month < 1 or month > 12:
        raise HTTPException(status_code=400, detail="月份必须在1-12之间")

    if year < 1900 or year > 3000:
        raise HTTPException(status_code=400, detail="年份必须在1900-3000之间")

    try:
        # 使用 ReportService 进行正确的月度报告计算
        from app.services.report_service import ReportService

        service = ReportService(db)
        report = await service.get_monthly_report(year, month, end_date)

        # 转换为API响应格式
        dormitory_allocations = []
        for detail in report.daily_details:
            # 按宿舍分组统计
            dorm_found = False
            for dorm_alloc in dormitory_allocations:
                if dorm_alloc["dormitory_name"] == detail.dormitory_name:
                    # 累加该宿舍的数据
                    for dept_alloc in detail.department_allocations:
                        dept_id = dept_alloc["department_id"]
                        if dept_id not in dorm_alloc["departments"]:
                            dorm_alloc["departments"][dept_id] = {
                                "department_name": dept_alloc["department_name"],
                                "bed_days": 0
                            }
                        dorm_alloc["departments"][dept_id]["bed_days"] += detail.total_beds * dept_alloc["allocation_ratio"]
                    dorm_found = True
                    break

            if not dorm_found:
                # 新宿舍
                departments = {}
                for dept_alloc in detail.department_allocations:
                    dept_id = dept_alloc["department_id"]
                    departments[dept_id] = {
                        "department_name": dept_alloc["department_name"],
                        "bed_days": detail.total_beds * dept_alloc["allocation_ratio"]
                    }

                dormitory_allocations.append({
                    "dormitory_name": detail.dormitory_name,
                    "departments": departments
                })

        # 计算宿舍内各部门占比
        for dorm_alloc in dormitory_allocations:
            # 计算该宿舍的总床位天数
            dorm_total_days = sum(dept_info["bed_days"] for dept_info in dorm_alloc["departments"].values())
            for dept_info in dorm_alloc["departments"].values():
                if dorm_total_days > 0:
                    dept_info["percentage_in_dorm"] = round((dept_info["bed_days"] / dorm_total_days) * 100, 1)

        # 计算统计天数
        from datetime import datetime, timedelta
        import calendar
        start_date = date(year, month, 1)
        month_last_day = date(year, month, calendar.monthrange(year, month)[1])
        today = date.today()

        if end_date:
            actual_end_date = min(end_date, month_last_day)
        else:
            if year == today.year and month == today.month and today.day > 1:
                actual_end_date = date(year, month, today.day - 1)
            else:
                actual_end_date = month_last_day

        return {
            "dormitory_allocations": dormitory_allocations,
            "year": year,
            "month": month,
            "start_date": start_date.isoformat(),
            "end_date": actual_end_date.isoformat(),
            "is_current_month": year == date.today().year and month == date.today().month
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取月度报告失败: {str(e)}")


# 日度分摊明细功能暂时移除，可以后续根据需要添加


@router.get("/realtime", summary="获取实时报告")
async def get_realtime_report(
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """获取当前系统实时统计数据"""
    try:
        # 简化的实时报告，直接查询数据库统计
        from app.repositories.department_repo import DepartmentRepository
        from app.repositories.dormitory_repo import DormitoryRepository
        from app.repositories.resident_repo import ResidentRepository
        from app.repositories.record_repo import RecordRepository

        dept_repo = DepartmentRepository(db)
        dorm_repo = DormitoryRepository(db)
        resident_repo = ResidentRepository(db)
        record_repo = RecordRepository(db)

        # 获取基础统计数据
        departments = dept_repo.get_all()
        dormitories = dorm_repo.get_all()
        residents = resident_repo.get_all()
        active_records = record_repo.get_active_records()

        # 计算统计数据
        total_departments = len([d for d in departments if d.is_active])
        total_dormitories = len([d for d in dormitories if d.is_active])
        total_beds = sum(d.total_beds for d in dormitories if d.is_active)
        occupied_beds = len(active_records)
        total_residents = len([r for r in residents if r.is_active])

        return {
            "total_departments": total_departments,
            "total_dormitories": total_dormitories,
            "total_beds": total_beds,
            "occupied_beds": occupied_beds,
            "available_beds": total_beds - occupied_beds,
            "total_residents": total_residents,
            "occupancy_rate": round((occupied_beds / total_beds * 100) if total_beds > 0 else 0, 1),
            "active_records": len(active_records)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时报告失败: {str(e)}")


@router.get("/monthly/{year}/{month}/export", summary="导出月度报告")
async def export_monthly_report(
    year: int,
    month: int,
    format: str = Query("excel", regex="^(excel|pdf|csv)$", description="导出格式"),
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    导出指定年月的费用分摊报告

    - **year**: 年份
    - **month**: 月份 (1-12)
    - **format**: 导出格式 (excel, pdf, csv)
    - **end_date**: 截止日期，不指定则统计整月
    """
    if month < 1 or month > 12:
        raise HTTPException(status_code=400, detail="月份必须在1-12之间")

    if year < 1900 or year > 3000:
        raise HTTPException(status_code=400, detail="年份必须在1900-3000之间")

    try:
        # 使用 ReportService 获取月度报告数据
        from app.services.report_service import ReportService

        service = ReportService(db)
        report = await service.get_monthly_report(year, month, end_date)

        # 转换报告数据为导出格式（复用月度报告API的逻辑）
        dormitory_allocations = []
        for detail in report.daily_details:
            # 按宿舍分组统计
            dorm_found = False
            for dorm_alloc in dormitory_allocations:
                if dorm_alloc["dormitory_name"] == detail.dormitory_name:
                    # 累加该宿舍的数据
                    for dept_alloc in detail.department_allocations:
                        dept_id = dept_alloc["department_id"]
                        if dept_id not in dorm_alloc["departments"]:
                            dorm_alloc["departments"][dept_id] = {
                                "department_name": dept_alloc["department_name"],
                                "bed_days": 0
                            }
                        dorm_alloc["departments"][dept_id]["bed_days"] += detail.total_beds * dept_alloc["allocation_ratio"]
                    dorm_found = True
                    break

            if not dorm_found:
                # 新宿舍
                departments = {}
                for dept_alloc in detail.department_allocations:
                    dept_id = dept_alloc["department_id"]
                    departments[dept_id] = {
                        "department_name": dept_alloc["department_name"],
                        "bed_days": detail.total_beds * dept_alloc["allocation_ratio"]
                    }

                dormitory_allocations.append({
                    "dormitory_name": detail.dormitory_name,
                    "departments": departments
                })

        # 计算宿舍内各部门占比
        for dorm_alloc in dormitory_allocations:
            # 计算该宿舍的总床位天数
            dorm_total_days = sum(dept_info["bed_days"] for dept_info in dorm_alloc["departments"].values())
            for dept_info in dorm_alloc["departments"].values():
                if dorm_total_days > 0:
                    dept_info["percentage_in_dorm"] = round((dept_info["bed_days"] / dorm_total_days) * 100, 1)

        # 计算统计天数
        from datetime import datetime, timedelta
        import calendar

        start_date = date(year, month, 1)
        month_last_day = date(year, month, calendar.monthrange(year, month)[1])
        today = date.today()

        if end_date:
            actual_end_date = min(end_date, month_last_day)
        else:
            if year == today.year and month == today.month and today.day > 1:
                actual_end_date = date(year, month, today.day - 1)
            else:
                actual_end_date = month_last_day

        # 准备导出数据
        report_data = {
            "dormitory_allocations": dormitory_allocations,
            "year": year,
            "month": month,
            "start_date": start_date.isoformat(),
            "end_date": actual_end_date.isoformat(),
            "is_current_month": year == date.today().year and month == date.today().month
        }

        # 获取住户详细信息用于导出
        # 需要获取实际的住户数据来填充导出格式
        from app.repositories.record_repo import RecordRepository
        from app.repositories.dormitory_repo import DormitoryRepository

        record_repo = RecordRepository(db)
        dormitory_repo = DormitoryRepository(db)

        # 获取日期范围内的所有入住记录
        date_range = service.calculator.get_month_date_range(year, month, end_date)
        residence_records = record_repo.get_records_in_date_range(date_range[0], date_range[-1])

        # 构建导出数据，使用与月度报告API相同的分摊比例
        export_data = {
            "year": year,
            "month": month,
            "start_date": start_date.isoformat(),
            "end_date": actual_end_date.isoformat(),
            "dormitories": []
        }

        # 为每个宿舍构建导出数据
        for dorm_alloc in dormitory_allocations:
            dorm_name = dorm_alloc["dormitory_name"]

            # 获取该宿舍的住户记录
            dorm_records = [r for r in residence_records
                          if r.dormitory and r.dormitory.name == dorm_name]

            # 按部门分组住户
            dept_residents = {}
            for record in dorm_records:
                dept_name = record.resident.department.name if record.resident.department else "未分配部门"
                if dept_name not in dept_residents:
                    dept_residents[dept_name] = []
                dept_residents[dept_name].append({
                    "name": record.resident.name,
                    "department": dept_name,
                    "project_group": record.project_group or "",
                    "bed_number": record.bed_number or ""
                })

            # 构建部门数据，使用月度报告API的分摊比例
            departments = []
            for dept_id, dept_info in dorm_alloc["departments"].items():
                dept_name = dept_info["department_name"]
                residents = dept_residents.get(dept_name, [])

                departments.append({
                    "department_name": dept_name,
                    "percentage": dept_info.get("percentage_in_dorm", 0.0),
                    "residents": sorted(residents, key=lambda x: x.get("bed_number", ""))
                })

            # 按占比排序部门
            departments.sort(key=lambda x: x["percentage"], reverse=True)

            export_data["dormitories"].append({
                "dormitory_name": dorm_name,
                "total_residents": len(dorm_records),
                "departments": departments
            })

        # 使用导出工具导出文件
        export_utils = ExportUtils()

        if format == "excel":
            file_content, filename, media_type = await export_utils.export_to_excel(export_data)
        elif format == "pdf":
            file_content, filename, media_type = await export_utils.export_to_pdf(export_data)
        elif format == "csv":
            file_content, filename, media_type = await export_utils.export_to_csv(export_data)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的导出格式: {format}")

        # 对文件名进行URL编码以支持中文
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename.encode('utf-8'))

        return Response(
            content=file_content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"}
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出月度报告失败: {str(e)}")


@router.get("/monthly/{year}/{month}/distribution", response_model=MonthlyDistributionResponse, summary="获取月度宿舍部门分布详情")
async def get_monthly_distribution_details(
    year: int,
    month: int,
    end_date: Optional[date] = Query(None, description="截止日期，用于实时统计"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取指定年月的宿舍部门分布详情数据

    展示所选月份的入住记录，包含：
    - 每个宿舍的入住记录详情
    - 住户姓名、部门、项目组、床位号
    - 入住日期和离开日期
    - 在该月份的入住天数
    - 各部门在每个宿舍的人数统计

    - **year**: 年份
    - **month**: 月份 (1-12)
    - **end_date**: 截止日期，不指定则统计整月
    """
    if month < 1 or month > 12:
        raise HTTPException(status_code=400, detail="月份必须在1-12之间")

    if year < 1900 or year > 3000:
        raise HTTPException(status_code=400, detail="年份必须在1900-3000之间")

    try:
        service = ReportService(db)
        return await service.get_monthly_distribution_details(year, month, end_date)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取月度宿舍分布详情失败: {str(e)}")
