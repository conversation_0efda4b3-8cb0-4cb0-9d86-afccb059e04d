"""
入住记录管理API
"""
from typing import List, Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db
from app.services.record_service import RecordService
from app.schemas.record import RecordCreate, RecordUpdate, RecordResponse, RecordFilter
from app.core.logging import logger
from app.auth.dependencies import get_current_user

router = APIRouter()


@router.get("/", response_model=List[RecordResponse])
def get_records(
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="限制记录数"),
    dormitory_id: Optional[str] = Query(None, description="宿舍ID筛选"),
    department_id: Optional[str] = Query(None, description="部门ID筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    check_in_start: Optional[date] = Query(None, description="入住开始日期"),
    check_in_end: Optional[date] = Query(None, description="入住结束日期"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取入住记录列表
    
    支持以下筛选条件：
    - dormitory_id: 按宿舍筛选
    - department_id: 按部门筛选
    - status: 按状态筛选 (ACTIVE/COMPLETED/CANCELLED)
    - check_in_start/check_in_end: 按入住日期范围筛选
    """
    try:
        service = RecordService(db)
        filters = RecordFilter(
            dormitory_id=dormitory_id,
            department_id=department_id,
            status=status,
            check_in_start=check_in_start,
            check_in_end=check_in_end
        )
        return service.get_records(skip=skip, limit=limit, filters=filters)
    except Exception as e:
        logger.error(f"获取入住记录列表API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=RecordResponse)
def create_record(
    record_data: RecordCreate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    创建入住记录

    为住户创建新的入住记录，需要提供：
    - 住户ID（必填）
    - 宿舍ID（必填）
    - 床位号（可选，系统自动分配）
    - 入住日期（必填）
    - 备注（可选）

    系统会自动验证：
    - 住户和宿舍是否存在
    - 住户是否已有活跃记录
    - 自动分配可用床位（如果未指定床位号）
    """
    try:
        service = RecordService(db)
        return service.create_record(record_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/active", response_model=List[RecordResponse])
def get_active_records(
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取所有活跃的入住记录
    
    返回当前所有状态为ACTIVE的入住记录
    """
    try:
        service = RecordService(db)
        return service.get_active_records()
    except Exception as e:
        logger.error(f"获取活跃入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{record_id}", response_model=RecordResponse)
def get_record(
    record_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取入住记录详情
    
    根据记录ID获取详细信息，包括：
    - 入住信息
    - 住户信息
    - 宿舍信息
    - 已住天数
    """
    try:
        service = RecordService(db)
        record = service.get_record(record_id)
        if not record:
            raise HTTPException(status_code=404, detail="入住记录不存在")
        return record
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取入住记录详情API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{record_id}", response_model=RecordResponse)
def update_record(
    record_id: str,
    record_data: RecordUpdate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    更新入住记录

    可以更新以下信息：
    - 入住日期（会检查床位可用性）
    - 离开日期
    - 状态
    - 备注

    注意：
    - 更新入住日期时会验证床位在新时间段内是否可用
    - 设置离开日期会自动将状态改为COMPLETED
    - 离开日期不能早于入住日期
    """
    try:
        service = RecordService(db)
        record = service.update_record(record_id, record_data)
        if not record:
            raise HTTPException(status_code=404, detail="入住记录不存在")
        return record
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{record_id}")
def delete_record(
    record_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    删除入住记录
    
    删除入住记录，注意：
    - 只能删除已完成或已取消的记录
    - 不能删除活跃状态的记录
    - 删除操作不可恢复
    """
    try:
        service = RecordService(db)
        success = service.delete_record(record_id)
        if not success:
            raise HTTPException(status_code=404, detail="入住记录不存在")
        return {"message": "入住记录删除成功"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{record_id}/checkout", response_model=RecordResponse)
def checkout_resident(
    record_id: str,
    checkout_date: date = Query(..., description="离开日期"),
    notes: Optional[str] = Query(None, description="备注"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    办理住户离开
    
    为活跃的入住记录办理离开手续：
    - 设置离开日期
    - 状态改为COMPLETED
    - 可添加备注
    """
    try:
        service = RecordService(db)
        record = service.checkout_resident(record_id, checkout_date, notes)
        if not record:
            raise HTTPException(status_code=404, detail="入住记录不存在")
        return record
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"办理住户离开API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/resident/{resident_id}", response_model=List[RecordResponse])
def get_records_by_resident(
    resident_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取住户的所有入住记录
    
    返回指定住户的历史入住记录
    """
    try:
        service = RecordService(db)
        # 使用筛选条件获取特定住户的记录
        filters = RecordFilter()
        all_records = service.get_records(skip=0, limit=1000, filters=filters)
        
        # 筛选出指定住户的记录
        resident_records = [r for r in all_records if r.resident_id == resident_id]
        
        return resident_records
    except Exception as e:
        logger.error(f"获取住户入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/dormitory/{dormitory_id}", response_model=List[RecordResponse])
def get_records_by_dormitory(
    dormitory_id: str,
    status: Optional[str] = Query(None, description="状态筛选"),
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    """
    获取宿舍的所有入住记录
    
    返回指定宿舍的入住记录，可按状态筛选
    """
    try:
        service = RecordService(db)
        filters = RecordFilter(dormitory_id=dormitory_id, status=status)
        return service.get_records(skip=0, limit=1000, filters=filters)
    except Exception as e:
        logger.error(f"获取宿舍入住记录API失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
