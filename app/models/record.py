"""
入住记录模型
"""
from sqlalchemy import Column, String, Integer, Date, Text, ForeignKey, CheckConstraint
from sqlalchemy.orm import relationship
from .base import BaseModel


class ResidenceRecord(BaseModel):
    """入住记录模型"""
    __tablename__ = "residence_records"
    
    resident_id = Column(String(50), ForeignKey("residents.id"), nullable=False, comment="住户ID")
    dormitory_id = Column(String(50), ForeignKey("dormitories.id"), nullable=False, comment="宿舍ID")
    bed_number = Column(Integer, nullable=False, comment="床位号")
    project_group = Column(String(100), nullable=False, comment="项目组")
    check_in_date = Column(Date, nullable=False, comment="入住日期")
    check_out_date = Column(Date, comment="离开日期")
    status = Column(String(20), default="ACTIVE", comment="状态")
    notes = Column(Text, comment="备注")
    
    # 约束
    __table_args__ = (
        CheckConstraint('check_out_date IS NULL OR check_out_date >= check_in_date', 
                       name='chk_checkout_after_checkin'),
        CheckConstraint('bed_number > 0', name='chk_bed_number_positive'),
        CheckConstraint("status IN ('ACTIVE', 'COMPLETED', 'CANCELLED')", name='chk_status_valid'),
    )
    
    # 关联关系
    resident = relationship("Resident", back_populates="residence_records")
    dormitory = relationship("Dormitory", back_populates="residence_records")
    
    def __repr__(self):
        return f"<ResidenceRecord(id={self.id}, resident={self.resident_id}, dormitory={self.dormitory_id})>"
