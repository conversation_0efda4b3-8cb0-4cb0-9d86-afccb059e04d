version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bedsharing-backend
    ports:
      - "8000:8000"
    volumes:
      - backend_logs:/app/logs
      - backend_exports:/app/exports
    environment:
      - DEBUG=false
      - DATABASE_URL=sqlite:///./dormitory_management.db
      - SECRET_KEY=change-this-secret-key-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - CORS_ORIGINS=["http://localhost:3000"]
      - LOG_LEVEL=INFO

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bedsharing-frontend
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy

volumes:
  backend_logs:
    driver: local
  backend_exports:
    driver: local