# 生产环境配置文件
# 复制此文件为 .env.prod 并修改相应的值

# 应用配置
APP_NAME=宿舍入住管理系统
APP_VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO

# 安全配置 - 请务必修改这些值
SECRET_KEY=CHANGE-THIS-TO-A-SECURE-RANDOM-STRING-IN-PRODUCTION
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
DATABASE_URL=postgresql://bedsharing:${POSTGRES_PASSWORD}@postgres:5432/bedsharing

# PostgreSQL配置
POSTGRES_DB=bedsharing
POSTGRES_USER=bedsharing
POSTGRES_PASSWORD=CHANGE-THIS-POSTGRES-PASSWORD

# Redis配置
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_PASSWORD=CHANGE-THIS-REDIS-PASSWORD

# 域名配置
DOMAIN=yourdomain.com

# CORS配置
CORS_ORIGINS=["https://yourdomain.com"]

# LDAP配置（如果使用LDAP认证）
LDAP_SERVER=ldap://your-ldap-server:389
LDAP_BASE_DN=dc=yourdomain,dc=com
LDAP_USER_DN=cn=admin,dc=yourdomain,dc=com
LDAP_PASSWORD=your-ldap-password

# 邮件配置（用于通知）
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>

# 文件配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份

# 监控配置（可选）
SENTRY_DSN=https://<EMAIL>/project-id
ENABLE_METRICS=true
