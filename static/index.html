<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宿舍入住管理系统</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.4.0/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.js"></script>
    
    <!-- Axios -->
    <script src="https://unpkg.com/axios@1.6.2/dist/axios.min.js"></script>
    
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <!-- Day.js -->
    <script src="https://unpkg.com/dayjs@1.11.10/dayjs.min.js"></script>
    <script src="https://unpkg.com/dayjs@1.11.10/locale/zh-cn.js"></script>
    
    <!-- NProgress -->
    <link rel="stylesheet" href="https://unpkg.com/nprogress@0.2.0/nprogress.css">
    <script src="https://unpkg.com/nprogress@0.2.0/nprogress.js"></script>
    
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
                'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        
        #app {
            min-height: 100vh;
        }
        
        /* 登录页面样式 */
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-box {
            background: white;
            border-radius: 12px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header .logo {
            margin-bottom: 20px;
        }
        
        .login-header h2 {
            color: #303133;
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .login-header p {
            color: #909399;
            margin: 0;
            font-size: 14px;
        }
        
        .login-button {
            width: 100%;
            margin-top: 20px;
        }
        
        .demo-accounts {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ebeef5;
        }
        
        .demo-accounts h4 {
            color: #606266;
            margin: 0 0 15px 0;
            font-size: 14px;
            text-align: center;
        }
        
        .demo-buttons {
            display: flex;
            gap: 10px;
        }
        
        /* 主布局样式 */
        .app-layout {
            height: 100vh;
            display: flex;
        }
        
        .app-sidebar {
            width: 200px;
            background-color: #304156;
            transition: width 0.3s;
            flex-shrink: 0;
        }
        
        .app-sidebar.collapsed {
            width: 64px;
        }
        
        .sidebar-header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px solid #434a50;
            color: white;
        }
        
        .sidebar-header h2 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .sidebar-menu {
            background-color: #304156;
            border-right: none;
        }
        
        .sidebar-menu .el-menu-item {
            color: #bfcbd9;
        }
        
        .sidebar-menu .el-menu-item:hover,
        .sidebar-menu .el-menu-item.is-active {
            background-color: #263445;
            color: #ffffff;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .app-header {
            height: 60px;
            background-color: white;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            flex-shrink: 0;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            color: #606266;
            transition: all 0.3s;
        }
        
        .user-info:hover {
            background-color: #f5f7fa;
            color: #409eff;
        }
        
        .app-main {
            flex: 1;
            background-color: #f5f7fa;
            padding: 20px;
            overflow-y: auto;
        }
        
        /* 页面内容样式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #303133;
        }
        
        .app-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
        
        .stats-item {
            display: flex;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .stats-icon {
            font-size: 32px;
            margin-right: 15px;
        }
        
        .stats-content .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .stats-content .stats-label {
            font-size: 14px;
            color: #909399;
        }
        
        .chart-container {
            height: 400px;
            margin-top: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-sidebar {
                width: 64px;
            }
            
            .app-main {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        
        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #909399;
        }
        
        /* 表格样式 */
        .el-table {
            margin-top: 20px;
        }
        
        /* 表单样式 */
        .form-container {
            max-width: 600px;
        }
        
        /* 操作按钮组 */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        /* 搜索栏 */
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .search-bar .el-input {
            width: 200px;
        }

        /* 宿舍卡片样式 */
        .dormitory-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .dormitory-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            background: white;
            overflow: hidden;
        }

        .dormitory-card .card-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dormitory-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .dormitory-icon {
            color: #409eff;
            font-size: 18px;
        }

        .dormitory-name {
            font-weight: 600;
            color: #303133;
        }

        .dormitory-card .card-body {
            padding: 20px;
        }

        .department-usage-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-weight: 500;
            color: #606266;
        }

        .department-tags {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .department-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .department-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .dept-icon {
            color: #67c23a;
            font-size: 14px;
        }

        .dept-name {
            font-weight: 500;
            color: #303133;
        }

        .department-percentage {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .percentage-value {
            font-weight: 600;
            color: #409eff;
            min-width: 40px;
            text-align: right;
        }

        .percentage-bar {
            height: 6px;
            background: #409eff;
            border-radius: 3px;
            min-width: 60px;
            max-width: 100px;
        }

        /* 分布详情样式 */
        .distribution-tables {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .dormitory-section {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            overflow: hidden;
        }

        .dormitory-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e4e7ed;
        }

        .dormitory-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
        }

        .dormitory-title .el-tag {
            margin-left: 10px;
        }

        .distribution-table {
            margin: 0;
        }

        .empty-dormitory {
            padding: 40px;
            text-align: center;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e4e7ed;
        }

        /* 空状态样式 */
        .empty-state {
            padding: 60px 20px;
            text-align: center;
        }

        .loading-placeholder {
            padding: 40px 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div v-if="currentView === 'login'" class="login-container">
            <div class="login-box">
                <div class="login-header">
                    <div class="logo">
                        <el-icon size="40" color="#409eff">
                            <House />
                        </el-icon>
                    </div>
                    <h2>宿舍入住管理系统</h2>
                    <p>请输入您的账号和密码登录</p>
                </div>
                
                <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" @keyup.enter="handleLogin">
                    <el-form-item prop="username">
                        <el-input
                            v-model="loginForm.username"
                            placeholder="请输入用户名"
                            size="large"
                            :prefix-icon="User"
                            clearable
                        />
                    </el-form-item>
                    
                    <el-form-item prop="password">
                        <el-input
                            v-model="loginForm.password"
                            type="password"
                            placeholder="请输入密码"
                            size="large"
                            :prefix-icon="Lock"
                            show-password
                            clearable
                        />
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button
                            type="primary"
                            size="large"
                            class="login-button"
                            :loading="isLoading"
                            @click="handleLogin"
                        >
                            {{ isLoading ? '登录中...' : '登录' }}
                        </el-button>
                    </el-form-item>
                </el-form>
                
                <div class="demo-accounts">
                    <h4>演示账号</h4>
                    <div class="demo-buttons">
                        <el-button size="small" @click="fillDemoAccount('testuser')">
                            测试用户
                        </el-button>
                        <el-button size="small" @click="fillDemoAccount('admin')">
                            管理员
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主应用布局 -->
        <div v-else class="app-layout">
            <!-- 侧边栏 -->
            <div class="app-sidebar" :class="{ collapsed: isCollapsed }">
                <div class="sidebar-header">
                    <h2 v-if="!isCollapsed">宿舍管理系统</h2>
                    <el-icon v-else size="24"><House /></el-icon>
                </div>
                
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    background-color="#304156"
                    text-color="#bfcbd9"
                    active-text-color="#ffffff"
                    class="sidebar-menu"
                >
                    <el-menu-item
                        v-for="route in menuRoutes"
                        :key="route.path"
                        :index="route.path"
                        @click="navigateTo(route.path)"
                    >
                        <el-icon>
                            <component :is="route.icon" />
                        </el-icon>
                        <template #title>{{ route.title }}</template>
                    </el-menu-item>
                </el-menu>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-container">
                <!-- 顶部导航 -->
                <div class="app-header">
                    <div class="header-left">
                        <el-button text @click="toggleSidebar">
                            <el-icon size="20">
                                <Expand v-if="isCollapsed" />
                                <Fold v-else />
                            </el-icon>
                        </el-button>
                        
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item>{{ getCurrentPageTitle() }}</el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    
                    <div class="header-right">
                        <el-button text @click="refreshPage">
                            <el-icon><Refresh /></el-icon>
                        </el-button>
                        
                        <el-dropdown @command="handleUserCommand">
                            <div class="user-info">
                                <el-icon><User /></el-icon>
                                <span>{{ userInfo?.username || '用户' }}</span>
                                <el-icon><ArrowDown /></el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="logout">
                                        <el-icon><SwitchButton /></el-icon>
                                        退出登录
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                
                <!-- 主内容 -->
                <div class="app-main">
                    <!-- 报表统计页面 -->
                    <div v-if="currentView === 'reports'">
                        <div class="page-header">
                            <h1 class="page-title">报表统计</h1>
                            <div class="page-actions">
                                <el-button type="primary" @click="refreshRealtimeReport">
                                    <el-icon><Refresh /></el-icon>
                                    刷新实时数据
                                </el-button>
                            </div>
                        </div>
                        
                        <!-- 实时报告 -->
                        <div class="app-card">
                            <div class="card-header">
                                <h3 class="card-title">实时报告</h3>
                                <el-button text @click="refreshRealtimeReport">
                                    <el-icon><Refresh /></el-icon>
                                </el-button>
                            </div>
                            
                            <div v-if="realtimeReport" class="realtime-content">
                                <el-row :gutter="20">
                                    <el-col :xs="24" :sm="12" :md="6">
                                        <div class="stats-item">
                                            <el-icon class="stats-icon" style="color: #409eff">
                                                <OfficeBuilding />
                                            </el-icon>
                                            <div class="stats-content">
                                                <div class="stats-value">{{ realtimeReport.total_departments }}</div>
                                                <div class="stats-label">总部门数</div>
                                            </div>
                                        </div>
                                    </el-col>
                                    <el-col :xs="24" :sm="12" :md="6">
                                        <div class="stats-item">
                                            <el-icon class="stats-icon" style="color: #67c23a">
                                                <House />
                                            </el-icon>
                                            <div class="stats-content">
                                                <div class="stats-value">{{ realtimeReport.total_dormitories }}</div>
                                                <div class="stats-label">总宿舍数</div>
                                            </div>
                                        </div>
                                    </el-col>
                                    <el-col :xs="24" :sm="12" :md="6">
                                        <div class="stats-item">
                                            <el-icon class="stats-icon" style="color: #e6a23c">
                                                <User />
                                            </el-icon>
                                            <div class="stats-content">
                                                <div class="stats-value">{{ realtimeReport.total_beds }}</div>
                                                <div class="stats-label">总床位数</div>
                                            </div>
                                        </div>
                                    </el-col>
                                    <el-col :xs="24" :sm="12" :md="6">
                                        <div class="stats-item">
                                            <el-icon class="stats-icon" style="color: #f56c6c">
                                                <TrendCharts />
                                            </el-icon>
                                            <div class="stats-content">
                                                <div class="stats-value">{{ realtimeReport.occupancy_rate }}%</div>
                                                <div class="stats-label">入住率</div>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                            <div v-else class="loading">
                                <el-icon class="is-loading"><Loading /></el-icon>
                                <p>加载中...</p>
                            </div>
                        </div>

                        <!-- 月度报告 -->
                        <div class="app-card">
                            <div class="card-header">
                                <h3 class="card-title">月度报告</h3>
                                <div>
                                    <el-date-picker
                                        v-model="selectedMonth"
                                        type="month"
                                        placeholder="选择月份"
                                        format="YYYY年MM月"
                                        value-format="YYYY-MM"
                                        @change="loadMonthlyReport"
                                    />
                                    <el-button type="primary" @click="exportMonthlyReport" style="margin-left: 10px;">
                                        <el-icon><Download /></el-icon>
                                        导出Excel
                                    </el-button>
                                </div>
                            </div>

                            <div v-if="currentMonthlyReport" class="monthly-content">
                                <!-- 宿舍分摊占比 -->
                                <h4 style="margin-top: 30px; margin-bottom: 20px">
                                    <el-icon style="margin-right: 8px; color: var(--el-color-primary)">
                                        <House />
                                    </el-icon>
                                    宿舍分摊占比
                                </h4>

                                <!-- 宿舍卡片式展示 -->
                                <div class="dormitory-cards" v-if="currentMonthlyReport.dormitory_allocations">
                                    <div
                                        v-for="dorm in currentMonthlyReport.dormitory_allocations"
                                        :key="dorm.dormitory_name"
                                        class="dormitory-card"
                                    >
                                        <div class="card-header">
                                            <div class="dormitory-info">
                                                <el-icon class="dormitory-icon">
                                                    <House />
                                                </el-icon>
                                                <span class="dormitory-name">{{ dorm.dormitory_name }}</span>
                                            </div>
                                            <div class="allocation-badge">
                                                <el-tag type="info" size="large">宿舍</el-tag>
                                            </div>
                                        </div>

                                        <div class="card-body">
                                            <div class="department-usage-title">
                                                <el-icon style="margin-right: 4px">
                                                    <OfficeBuilding />
                                                </el-icon>
                                                部门分摊占比情况
                                            </div>
                                            <div class="department-tags">
                                                <div
                                                    v-for="(dept, deptId) in dorm.departments"
                                                    :key="deptId"
                                                    class="department-item"
                                                >
                                                    <div class="department-info">
                                                        <el-icon class="dept-icon">
                                                            <User />
                                                        </el-icon>
                                                        <span class="dept-name">{{ dept.department_name }}</span>
                                                    </div>
                                                    <div class="department-percentage">
                                                        <span class="percentage-value">{{ dept.percentage_in_dorm }}%</span>
                                                        <div class="percentage-bar" :style="{ width: dept.percentage_in_dorm + '%' }"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 如果没有数据 -->
                                <div v-if="!currentMonthlyReport.dormitory_allocations || currentMonthlyReport.dormitory_allocations.length === 0" class="empty-state">
                                    <el-empty description="暂无宿舍分摊数据">
                                        <el-icon style="font-size: 64px; color: var(--el-color-info)">
                                            <House />
                                        </el-icon>
                                    </el-empty>
                                </div>
                            </div>

                            <div v-else-if="!selectedMonth" class="empty-state">
                                <el-empty description="请选择月份查看报告" />
                            </div>
                        </div>

                        <!-- 宿舍部门分布详情 -->
                        <div class="app-card">
                            <div class="card-header">
                                <h3 class="card-title">宿舍部门分布详情</h3>
                                <div class="card-actions">
                                    <el-date-picker
                                        v-model="distributionMonth"
                                        type="month"
                                        placeholder="选择月份"
                                        format="YYYY年MM月"
                                        value-format="YYYY-MM"
                                        @change="fetchDistributionDetails"
                                        style="margin-right: 12px"
                                    />
                                    <el-button @click="fetchDistributionDetails" :loading="distributionLoading">
                                        <el-icon><Refresh /></el-icon>
                                        刷新
                                    </el-button>
                                </div>
                            </div>

                            <div v-if="distributionDetails && distributionMonth" class="distribution-content">
                                <!-- 宿舍详情表格 -->
                                <div class="distribution-tables">
                                    <div v-for="dormitory in distributionDetails.dormitories" :key="dormitory.dormitory_name" class="dormitory-section">
                                        <div class="dormitory-header">
                                            <h4 class="dormitory-title">
                                                <el-icon><House /></el-icon>
                                                {{ dormitory.dormitory_name }}
                                                <el-tag type="info" size="small">{{ dormitory.total_beds }}床位</el-tag>
                                                <el-tag v-if="dormitory.records.length === 0" type="warning" size="small">空宿舍</el-tag>
                                            </h4>
                                            <div v-if="Object.keys(dormitory.department_summary).length > 0" class="department-tags">
                                                <el-tag
                                                    v-for="(count, deptName) in dormitory.department_summary"
                                                    :key="deptName"
                                                    type="primary"
                                                    size="small"
                                                >
                                                    {{ deptName }}: {{ count }}人
                                                </el-tag>
                                            </div>
                                        </div>

                                        <el-table
                                            v-if="dormitory.records.length > 0"
                                            :data="dormitory.records"
                                            stripe
                                            size="small"
                                            class="distribution-table"
                                        >
                                            <el-table-column prop="bed_number" label="床位号" width="80" align="center" />
                                            <el-table-column prop="resident_name" label="姓名" width="100" />
                                            <el-table-column prop="department_name" label="部门" width="120" />
                                            <el-table-column prop="project_group" label="项目组" width="120" />
                                            <el-table-column prop="check_in_date" label="入住日期" width="110" align="center" />
                                            <el-table-column label="离开日期" width="110" align="center">
                                                <template #default="{ row }">
                                                    <span v-if="row.check_out_date">{{ row.check_out_date }}</span>
                                                    <el-tag v-else type="success" size="small">至今</el-tag>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="bed_days" label="床位天数" width="90" align="center" />
                                            <el-table-column prop="daily_cost" label="日费用" width="80" align="center" />
                                            <el-table-column prop="total_cost" label="总费用" width="90" align="center" />
                                        </el-table>

                                        <div v-else class="empty-dormitory">
                                            <el-empty description="该宿舍暂无入住记录" :image-size="60" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-else-if="!distributionMonth" class="empty-state">
                                <el-empty description="请选择月份查看分布详情" />
                            </div>

                            <div v-else class="loading-placeholder">
                                <el-skeleton :rows="4" animated />
                            </div>
                        </div>
                    </div>

                    <!-- 入住记录页面 -->
                    <div v-if="currentView === 'records'">
                        <div class="page-header">
                            <h1 class="page-title">入住记录</h1>
                            <div class="page-actions">
                                <el-button type="primary" @click="showCreateRecordDialog">
                                    <el-icon><Plus /></el-icon>
                                    新增记录
                                </el-button>
                                <el-button type="success" @click="showActiveRecords">
                                    <el-icon><View /></el-icon>
                                    活跃记录
                                </el-button>
                                <el-button @click="refreshRecords">
                                    <el-icon><Refresh /></el-icon>
                                    刷新
                                </el-button>
                            </div>
                        </div>

                        <!-- 搜索筛选 -->
                        <div class="app-card">
                            <el-form :model="recordSearchForm" inline>
                                <el-form-item label="宿舍">
                                    <el-select
                                        v-model="recordSearchForm.dormitory_id"
                                        placeholder="请选择宿舍"
                                        clearable
                                        style="width: 180px"
                                    >
                                        <el-option
                                            v-for="dorm in dormitories"
                                            :key="dorm.id"
                                            :label="dorm.name"
                                            :value="dorm.id"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="部门">
                                    <el-select
                                        v-model="recordSearchForm.department_id"
                                        placeholder="请选择部门"
                                        clearable
                                        style="width: 180px"
                                    >
                                        <el-option
                                            v-for="dept in departments"
                                            :key="dept.id"
                                            :label="dept.name"
                                            :value="dept.id"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="状态">
                                    <el-select
                                        v-model="recordSearchForm.status"
                                        placeholder="请选择状态"
                                        clearable
                                        style="width: 120px"
                                    >
                                        <el-option label="入住中" value="ACTIVE" />
                                        <el-option label="已离开" value="COMPLETED" />
                                        <el-option label="已取消" value="CANCELLED" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="入住日期">
                                    <el-date-picker
                                        v-model="recordDateRange"
                                        type="daterange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        format="YYYY-MM-DD"
                                        value-format="YYYY-MM-DD"
                                        style="width: 240px"
                                    />
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchRecords">
                                        <el-icon><Search /></el-icon>
                                        搜索
                                    </el-button>
                                    <el-button @click="resetRecordSearch">
                                        <el-icon><RefreshLeft /></el-icon>
                                        重置
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 记录列表 -->
                        <div class="app-card">
                            <el-table :data="filteredRecords" style="width: 100%" v-loading="recordsLoading" stripe>
                                <el-table-column type="index" label="#" width="50" />
                                <el-table-column prop="resident_name" label="住户姓名" width="100" />
                                <el-table-column prop="employee_id" label="工号" width="120" />
                                <el-table-column prop="department_name" label="部门" width="120" />
                                <el-table-column prop="project_group" label="项目组" width="120" />
                                <el-table-column prop="dormitory_name" label="宿舍" width="120" />
                                <el-table-column prop="bed_number" label="床位号" width="80" align="center" />
                                <el-table-column prop="check_in_date" label="入住日期" width="110" align="center" />
                                <el-table-column label="离开日期" width="110" align="center">
                                    <template #default="scope">
                                        <span v-if="scope.row.check_out_date">{{ scope.row.check_out_date }}</span>
                                        <el-tag v-else type="success" size="small">至今</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="100" align="center">
                                    <template #default="scope">
                                        <el-tag :type="getRecordStatusType(scope.row.status)" size="small">
                                            {{ getRecordStatusText(scope.row.status) }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="bed_days" label="床位天数" width="90" align="center" />
                                <el-table-column prop="total_cost" label="总费用" width="90" align="center" />
                                <el-table-column label="操作" width="200" fixed="right">
                                    <template #default="scope">
                                        <el-button size="small" @click="viewRecord(scope.row)">查看</el-button>
                                        <el-button size="small" @click="editRecord(scope.row)">编辑</el-button>
                                        <el-button
                                            v-if="scope.row.status === 'ACTIVE'"
                                            size="small"
                                            type="warning"
                                            @click="checkOutRecord(scope.row)"
                                        >
                                            离开
                                        </el-button>
                                        <el-button size="small" type="danger" @click="deleteRecord(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页 -->
                            <div class="pagination-container">
                                <el-pagination
                                    v-model:current-page="recordPagination.page"
                                    v-model:page-size="recordPagination.size"
                                    :page-sizes="[10, 20, 50, 100]"
                                    :total="recordPagination.total"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    @size-change="handleRecordSizeChange"
                                    @current-change="handleRecordPageChange"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- 部门管理页面 -->
                    <div v-if="currentView === 'departments'">
                        <div class="page-header">
                            <h1 class="page-title">部门管理</h1>
                            <div class="page-actions">
                                <el-button type="primary" @click="showCreateDepartmentDialog">
                                    <el-icon><Plus /></el-icon>
                                    新增部门
                                </el-button>
                                <el-button @click="refreshDepartments">
                                    <el-icon><Refresh /></el-icon>
                                    刷新
                                </el-button>
                            </div>
                        </div>

                        <!-- 搜索筛选 -->
                        <div class="app-card">
                            <el-form :model="departmentSearchForm" inline>
                                <el-form-item label="部门名称">
                                    <el-input
                                        v-model="departmentSearchForm.name"
                                        placeholder="请输入部门名称"
                                        clearable
                                        style="width: 200px"
                                    />
                                </el-form-item>
                                <el-form-item label="状态">
                                    <el-select
                                        v-model="departmentSearchForm.is_active"
                                        placeholder="请选择状态"
                                        clearable
                                        style="width: 120px"
                                    >
                                        <el-option label="启用" :value="true" />
                                        <el-option label="禁用" :value="false" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchDepartments">
                                        <el-icon><Search /></el-icon>
                                        搜索
                                    </el-button>
                                    <el-button @click="resetDepartmentSearch">
                                        <el-icon><RefreshLeft /></el-icon>
                                        重置
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 部门列表 -->
                        <div class="app-card">
                            <el-table :data="filteredDepartments" style="width: 100%" v-loading="departmentsLoading" stripe>
                                <el-table-column prop="name" label="部门名称" min-width="150" />
                                <el-table-column prop="description" label="描述" min-width="200">
                                    <template #default="{ row }">
                                        {{ row.description || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="is_active" label="状态" width="100">
                                    <template #default="{ row }">
                                        <el-tag :type="row.is_active ? 'success' : 'danger'">
                                            {{ row.is_active ? '启用' : '禁用' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="resident_count" label="住户数量" width="100">
                                    <template #default="{ row }">
                                        {{ row.resident_count || 0 }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180">
                                    <template #default="{ row }">
                                        {{ formatDate(row.created_at) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="200" fixed="right">
                                    <template #default="{ row }">
                                        <el-button type="primary" size="small" @click="showEditDepartmentDialog(row)">
                                            编辑
                                        </el-button>
                                        <el-button size="small" type="danger" @click="deleteDepartment(row)">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <!-- 新增/编辑部门对话框 -->
                        <el-dialog
                            v-model="departmentDialogVisible"
                            :title="isDepartmentEdit ? '编辑部门' : '新增部门'"
                            width="500px"
                            @close="resetDepartmentForm"
                        >
                            <el-form
                                ref="departmentFormRef"
                                :model="departmentFormData"
                                :rules="departmentFormRules"
                                label-width="80px"
                            >
                                <el-form-item label="部门名称" prop="name">
                                    <el-input
                                        v-model="departmentFormData.name"
                                        placeholder="请输入部门名称"
                                        maxlength="50"
                                        show-word-limit
                                    />
                                </el-form-item>
                                <el-form-item label="部门描述" prop="description">
                                    <el-input
                                        v-model="departmentFormData.description"
                                        type="textarea"
                                        :rows="3"
                                        placeholder="请输入部门描述"
                                        maxlength="200"
                                        show-word-limit
                                    />
                                </el-form-item>
                                <el-form-item label="状态" prop="is_active">
                                    <el-switch
                                        v-model="departmentFormData.is_active"
                                        active-text="启用"
                                        inactive-text="禁用"
                                    />
                                </el-form-item>
                            </el-form>

                            <template #footer>
                                <el-button @click="departmentDialogVisible = false">取消</el-button>
                                <el-button
                                    type="primary"
                                    :loading="departmentSubmitLoading"
                                    @click="handleDepartmentSubmit"
                                >
                                    {{ isDepartmentEdit ? '更新' : '创建' }}
                                </el-button>
                            </template>
                        </el-dialog>
                    </div>

                    <!-- 宿舍管理页面 -->
                    <div v-if="currentView === 'dormitories'">
                        <div class="page-header">
                            <h1 class="page-title">宿舍管理</h1>
                            <div class="page-actions">
                                <el-button type="primary" @click="showCreateDormitoryDialog">
                                    <el-icon><Plus /></el-icon>
                                    新增宿舍
                                </el-button>
                                <el-button @click="refreshDormitories">
                                    <el-icon><Refresh /></el-icon>
                                    刷新
                                </el-button>
                            </div>
                        </div>

                        <!-- 搜索筛选 -->
                        <div class="app-card">
                            <el-form :model="dormitorySearchForm" inline>
                                <el-form-item label="宿舍名称">
                                    <el-input
                                        v-model="dormitorySearchForm.name"
                                        placeholder="请输入宿舍名称"
                                        clearable
                                        style="width: 200px"
                                    />
                                </el-form-item>
                                <el-form-item label="状态">
                                    <el-select
                                        v-model="dormitorySearchForm.is_active"
                                        placeholder="请选择状态"
                                        clearable
                                        style="width: 120px"
                                    >
                                        <el-option label="启用" :value="true" />
                                        <el-option label="禁用" :value="false" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchDormitories">
                                        <el-icon><Search /></el-icon>
                                        搜索
                                    </el-button>
                                    <el-button @click="resetDormitorySearch">
                                        <el-icon><RefreshLeft /></el-icon>
                                        重置
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 宿舍列表 -->
                        <div class="app-card">
                            <el-table :data="filteredDormitories" style="width: 100%" v-loading="dormitoriesLoading" stripe>
                                <el-table-column prop="name" label="宿舍名称" min-width="150" />
                                <el-table-column prop="department_name" label="所属部门" min-width="120">
                                    <template #default="{ row }">
                                        {{ row.department_name || '未分配' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="total_beds" label="总床位" width="100" />
                                <el-table-column prop="occupied_beds" label="已占用" width="100">
                                    <template #default="{ row }">
                                        {{ row.occupied_beds || 0 }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="available_beds" label="可用床位" width="100">
                                    <template #default="{ row }">
                                        {{ row.available_beds || row.total_beds }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="入住率" width="100">
                                    <template #default="{ row }">
                                        <el-progress
                                            :percentage="getOccupancyRate(row)"
                                            :color="getProgressColor(getOccupancyRate(row))"
                                            :stroke-width="8"
                                        />
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="描述" min-width="200">
                                    <template #default="{ row }">
                                        {{ row.description || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="is_active" label="状态" width="100">
                                    <template #default="{ row }">
                                        <el-tag :type="row.is_active ? 'success' : 'danger'">
                                            {{ row.is_active ? '启用' : '禁用' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="250" fixed="right">
                                    <template #default="{ row }">
                                        <el-button type="primary" size="small" @click="showEditDormitoryDialog(row)">
                                            编辑
                                        </el-button>
                                        <el-button size="small" @click="viewDormitoryDetails(row)">
                                            详情
                                        </el-button>
                                        <el-button size="small" type="danger" @click="deleteDormitory(row)">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <!-- 新增/编辑宿舍对话框 -->
                        <el-dialog
                            v-model="dormitoryDialogVisible"
                            :title="isDormitoryEdit ? '编辑宿舍' : '新增宿舍'"
                            width="600px"
                            @close="resetDormitoryForm"
                        >
                            <el-form
                                ref="dormitoryFormRef"
                                :model="dormitoryFormData"
                                :rules="dormitoryFormRules"
                                label-width="100px"
                            >
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="宿舍名称" prop="name">
                                            <el-input
                                                v-model="dormitoryFormData.name"
                                                placeholder="请输入宿舍名称"
                                                maxlength="50"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="所属部门" prop="department_id">
                                            <el-select
                                                v-model="dormitoryFormData.department_id"
                                                placeholder="请选择部门"
                                                style="width: 100%"
                                            >
                                                <el-option
                                                    v-for="dept in departments"
                                                    :key="dept.id"
                                                    :label="dept.name"
                                                    :value="dept.id"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="总床位数" prop="total_beds">
                                            <el-input-number
                                                v-model="dormitoryFormData.total_beds"
                                                :min="1"
                                                :max="20"
                                                style="width: 100%"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="状态" prop="is_active">
                                            <el-switch
                                                v-model="dormitoryFormData.is_active"
                                                active-text="启用"
                                                inactive-text="禁用"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="宿舍描述" prop="description">
                                    <el-input
                                        v-model="dormitoryFormData.description"
                                        type="textarea"
                                        :rows="3"
                                        placeholder="请输入宿舍描述"
                                        maxlength="200"
                                        show-word-limit
                                    />
                                </el-form-item>
                            </el-form>

                            <template #footer>
                                <el-button @click="dormitoryDialogVisible = false">取消</el-button>
                                <el-button
                                    type="primary"
                                    :loading="dormitorySubmitLoading"
                                    @click="handleDormitorySubmit"
                                >
                                    {{ isDormitoryEdit ? '更新' : '创建' }}
                                </el-button>
                            </template>
                        </el-dialog>
                    </div>

                    <!-- 住户管理页面 -->
                    <div v-if="currentView === 'residents'">
                        <div class="page-header">
                            <h1 class="page-title">住户管理</h1>
                            <div class="page-actions">
                                <el-button type="primary" @click="showCreateResidentDialog">
                                    <el-icon><Plus /></el-icon>
                                    新增住户
                                </el-button>
                                <el-button @click="refreshResidents">
                                    <el-icon><Refresh /></el-icon>
                                    刷新
                                </el-button>
                            </div>
                        </div>

                        <!-- 搜索筛选 -->
                        <div class="app-card">
                            <el-form :model="residentSearchForm" inline>
                                <el-form-item label="搜索关键词">
                                    <el-input
                                        v-model="residentSearchForm.keyword"
                                        placeholder="姓名、员工号、电话、邮箱"
                                        clearable
                                        style="width: 250px"
                                    />
                                </el-form-item>
                                <el-form-item label="所属部门">
                                    <el-select
                                        v-model="residentSearchForm.department_id"
                                        placeholder="请选择部门"
                                        clearable
                                        style="width: 200px"
                                    >
                                        <el-option
                                            v-for="dept in departments"
                                            :key="dept.id"
                                            :label="dept.name"
                                            :value="dept.id"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="状态">
                                    <el-select
                                        v-model="residentSearchForm.is_active"
                                        placeholder="请选择状态"
                                        clearable
                                        style="width: 120px"
                                    >
                                        <el-option label="启用" :value="true" />
                                        <el-option label="禁用" :value="false" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="searchResidents">
                                        <el-icon><Search /></el-icon>
                                        搜索
                                    </el-button>
                                    <el-button @click="resetResidentSearch">
                                        <el-icon><RefreshLeft /></el-icon>
                                        重置
                                    </el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 住户列表 -->
                        <div class="app-card">
                            <el-table :data="filteredResidents" style="width: 100%" v-loading="residentsLoading" stripe>
                                <el-table-column prop="name" label="姓名" min-width="120" />
                                <el-table-column prop="employee_id" label="员工号" width="120">
                                    <template #default="{ row }">
                                        {{ row.employee_id || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="phone" label="电话" width="130">
                                    <template #default="{ row }">
                                        {{ row.phone || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="email" label="邮箱" min-width="180">
                                    <template #default="{ row }">
                                        {{ row.email || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="department_name" label="所属部门" width="150">
                                    <template #default="{ row }">
                                        {{ row.department_name || '-' }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="当前住宿" width="200">
                                    <template #default="{ row }">
                                        <div v-if="row.current_dormitory">
                                            <el-tag type="success" size="small">
                                                {{ row.current_dormitory.dormitory_name }}
                                            </el-tag>
                                            <span style="margin-left: 5px; color: #909399; font-size: 12px;">
                                                床位{{ row.current_dormitory.bed_number }}
                                            </span>
                                        </div>
                                        <span v-else style="color: #909399;">未入住</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="gender" label="性别" width="80">
                                    <template #default="{ row }">
                                        <el-tag :type="row.gender === 'MALE' ? 'primary' : 'warning'" size="small">
                                            {{ row.gender === 'MALE' ? '男' : '女' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="is_active" label="状态" width="100">
                                    <template #default="{ row }">
                                        <el-tag :type="row.is_active ? 'success' : 'danger'">
                                            {{ row.is_active ? '启用' : '禁用' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180">
                                    <template #default="{ row }">
                                        {{ formatDate(row.created_at) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="200" fixed="right">
                                    <template #default="{ row }">
                                        <el-button type="primary" size="small" @click="showEditResidentDialog(row)">
                                            编辑
                                        </el-button>
                                        <el-button size="small" type="danger" @click="deleteResident(row)">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <!-- 新增/编辑住户对话框 -->
                        <el-dialog
                            v-model="residentDialogVisible"
                            :title="isResidentEdit ? '编辑住户' : '新增住户'"
                            width="600px"
                            @close="resetResidentForm"
                        >
                            <el-form
                                ref="residentFormRef"
                                :model="residentFormData"
                                :rules="residentFormRules"
                                label-width="100px"
                            >
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="姓名" prop="name">
                                            <el-input
                                                v-model="residentFormData.name"
                                                placeholder="请输入姓名"
                                                maxlength="50"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="员工号" prop="employee_id">
                                            <el-input
                                                v-model="residentFormData.employee_id"
                                                placeholder="请输入员工号"
                                                maxlength="20"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="电话" prop="phone">
                                            <el-input
                                                v-model="residentFormData.phone"
                                                placeholder="请输入电话号码"
                                                maxlength="20"
                                            />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="邮箱" prop="email">
                                            <el-input
                                                v-model="residentFormData.email"
                                                placeholder="请输入邮箱地址"
                                                maxlength="100"
                                            />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="所属部门" prop="department_id">
                                            <el-select
                                                v-model="residentFormData.department_id"
                                                placeholder="请选择部门"
                                                style="width: 100%"
                                            >
                                                <el-option
                                                    v-for="dept in departments"
                                                    :key="dept.id"
                                                    :label="dept.name"
                                                    :value="dept.id"
                                                />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="性别" prop="gender">
                                            <el-radio-group v-model="residentFormData.gender">
                                                <el-radio value="MALE">男</el-radio>
                                                <el-radio value="FEMALE">女</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-form-item label="状态" prop="is_active">
                                    <el-switch
                                        v-model="residentFormData.is_active"
                                        active-text="启用"
                                        inactive-text="禁用"
                                    />
                                </el-form-item>
                            </el-form>

                            <template #footer>
                                <el-button @click="residentDialogVisible = false">取消</el-button>
                                <el-button
                                    type="primary"
                                    :loading="residentSubmitLoading"
                                    @click="handleResidentSubmit"
                                >
                                    {{ isResidentEdit ? '更新' : '创建' }}
                                </el-button>
                            </template>
                        </el-dialog>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue;
        const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
        
        // 配置 Day.js
        dayjs.locale('zh-cn');
        
        // 配置 NProgress
        NProgress.configure({ showSpinner: false });
        
        // API 基础配置
        const API_BASE_URL = '/api';
        
        // 创建 axios 实例
        const request = axios.create({
            baseURL: API_BASE_URL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        // 请求拦截器
        request.interceptors.request.use(
            (config) => {
                const token = localStorage.getItem('token');
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
        
        // 响应拦截器
        request.interceptors.response.use(
            (response) => {
                return response.data;
            },
            (error) => {
                const { response } = error;
                let message = '请求失败';
                
                if (response) {
                    switch (response.status) {
                        case 400:
                            message = response.data?.detail || '请求参数错误';
                            break;
                        case 401:
                            message = '登录已过期，请重新登录';
                            localStorage.removeItem('token');
                            localStorage.removeItem('userInfo');
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                            break;
                        case 403:
                            message = '拒绝访问';
                            break;
                        case 404:
                            message = '请求的资源不存在';
                            break;
                        case 422:
                            message = response.data?.detail || '数据验证失败';
                            break;
                        case 500:
                            message = response.data?.detail || '服务器内部错误';
                            break;
                        default:
                            message = `请求失败 (${response.status})`;
                    }
                } else if (error.code === 'ECONNABORTED') {
                    message = '请求超时';
                } else if (error.message === 'Network Error') {
                    message = '网络连接失败';
                }
                
                ElMessage.error(message);
                return Promise.reject(error);
            }
        );

        // API 方法
        const api = {
            // 认证相关
            auth: {
                login: (data) => request.post('/v1/auth/login', data),
                logout: () => request.post('/v1/auth/logout'),
                getCurrentUser: () => request.get('/v1/auth/me'),
                verifyToken: () => request.get('/v1/auth/verify')
            },

            // 报表相关
            reports: {
                getRealtimeReport: () => request.get('/v1/reports/realtime'),
                getMonthlyReport: (year, month) => request.get(`/v1/reports/monthly/${year}/${month}`),
                getMonthlyDistributionDetails: (year, month, endDate = null) => {
                    const params = {};
                    if (endDate) params.end_date = endDate;
                    return request.get(`/v1/reports/monthly/${year}/${month}/distribution`, { params });
                },
                getYearlySummary: (year) => request.get(`/v1/reports/summary/${year}`),
                exportMonthlyReport: (year, month, format = 'excel') =>
                    request.get(`/v1/reports/monthly/${year}/${month}/export`, {
                        params: { format },
                        responseType: 'blob'
                    })
            },

            // 部门相关
            departments: {
                getList: (params) => request.get('/v1/departments', { params }),
                create: (data) => request.post('/v1/departments', data),
                update: (id, data) => request.put(`/v1/departments/${id}`, data),
                delete: (id) => request.delete(`/v1/departments/${id}`),
                toggleStatus: (id) => request.patch(`/v1/departments/${id}/toggle-status`)
            },

            // 宿舍相关
            dormitories: {
                getList: (params) => request.get('/v1/dormitories', { params }),
                create: (data) => request.post('/v1/dormitories', data),
                update: (id, data) => request.put(`/v1/dormitories/${id}`, data),
                delete: (id) => request.delete(`/v1/dormitories/${id}`)
            },

            // 住户相关
            residents: {
                getList: (params) => request.get('/v1/residents', { params }),
                create: (data) => request.post('/v1/residents', data),
                update: (id, data) => request.put(`/v1/residents/${id}`, data),
                delete: (id) => request.delete(`/v1/residents/${id}`)
            },

            // 记录相关
            records: {
                getList: (params) => request.get('/v1/records', { params }),
                create: (data) => request.post('/v1/records', data),
                update: (id, data) => request.put(`/v1/records/${id}`, data),
                delete: (id) => request.delete(`/v1/records/${id}`)
            }
        };

        // 创建 Vue 应用
        const app = createApp({
            setup() {
                // 响应式状态
                const currentView = ref('login');
                const isCollapsed = ref(false);
                const activeMenu = ref('/reports');
                const isLoading = ref(false);
                const userInfo = ref(null);

                // 登录表单
                const loginForm = reactive({
                    username: '',
                    password: ''
                });

                const loginRules = {
                    username: [
                        { required: true, message: '请输入用户名', trigger: 'blur' },
                        { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
                    ],
                    password: [
                        { required: true, message: '请输入密码', trigger: 'blur' },
                        { min: 1, message: '密码不能为空', trigger: 'blur' }
                    ]
                };

                // 报表数据
                const realtimeReport = ref(null);
                const monthlyReports = ref([]);
                const yearlySummary = ref(null);
                const selectedMonth = ref(dayjs().format('YYYY-MM'));
                const currentMonthlyReport = ref(null);
                const distributionMonth = ref(dayjs().format('YYYY-MM'));
                const distributionDetails = ref(null);
                const distributionLoading = ref(false);

                // 其他数据
                const departments = ref([]);
                const dormitories = ref([]);
                const residents = ref([]);
                const records = ref([]);

                // 加载状态
                const departmentsLoading = ref(false);
                const dormitoriesLoading = ref(false);
                const residentsLoading = ref(false);
                const recordsLoading = ref(false);

                // 搜索表单
                const departmentSearchForm = reactive({
                    name: '',
                    is_active: null
                });

                const dormitorySearchForm = reactive({
                    name: '',
                    is_active: null
                });

                const residentSearchForm = reactive({
                    keyword: '',
                    department_id: '',
                    is_active: null
                });

                const recordSearchForm = reactive({
                    keyword: '',
                    status: '',
                    dormitory_id: '',
                    department_id: ''
                });

                const recordDateRange = ref([]);
                const filteredRecords = ref([]);
                const recordPagination = reactive({
                    page: 1,
                    size: 20,
                    total: 0
                });

                // 筛选后的数据
                const filteredDepartments = ref([]);
                const filteredDormitories = ref([]);
                const filteredResidents = ref([]);

                // 对话框状态
                const departmentDialogVisible = ref(false);
                const isDepartmentEdit = ref(false);
                const departmentSubmitLoading = ref(false);
                const departmentFormRef = ref();

                const dormitoryDialogVisible = ref(false);
                const isDormitoryEdit = ref(false);
                const dormitorySubmitLoading = ref(false);
                const dormitoryFormRef = ref();

                const residentDialogVisible = ref(false);
                const isResidentEdit = ref(false);
                const residentSubmitLoading = ref(false);
                const residentFormRef = ref();

                // 表单数据
                const departmentFormData = reactive({
                    name: '',
                    description: '',
                    is_active: true
                });

                const dormitoryFormData = reactive({
                    name: '',
                    department_id: '',
                    total_beds: 1,
                    description: '',
                    is_active: true
                });

                const residentFormData = reactive({
                    name: '',
                    employee_id: '',
                    phone: '',
                    email: '',
                    department_id: '',
                    gender: 'MALE',
                    is_active: true
                });

                // 表单验证规则
                const departmentFormRules = {
                    name: [
                        { required: true, message: '请输入部门名称', trigger: 'blur' },
                        { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' }
                    ]
                };

                const dormitoryFormRules = {
                    name: [
                        { required: true, message: '请输入宿舍名称', trigger: 'blur' },
                        { min: 2, max: 50, message: '宿舍名称长度在 2 到 50 个字符', trigger: 'blur' }
                    ],
                    total_beds: [
                        { required: true, message: '请输入总床位数', trigger: 'blur' }
                    ]
                };

                const residentFormRules = {
                    name: [
                        { required: true, message: '请输入姓名', trigger: 'blur' },
                        { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
                    ],
                    department_id: [
                        { required: true, message: '请选择所属部门', trigger: 'change' }
                    ],
                    gender: [
                        { required: true, message: '请选择性别', trigger: 'change' }
                    ]
                };

                // 菜单路由
                const menuRoutes = [
                    { path: '/reports', title: '报表统计', icon: 'TrendCharts' },
                    { path: '/records', title: '入住记录', icon: 'Document' },
                    { path: '/departments', title: '部门管理', icon: 'OfficeBuilding' },
                    { path: '/dormitories', title: '宿舍管理', icon: 'House' },
                    { path: '/residents', title: '住户管理', icon: 'User' }
                ];

                // 计算属性
                const isAuthenticated = computed(() => !!localStorage.getItem('token'));

                // 方法
                const handleLogin = async () => {
                    try {
                        isLoading.value = true;

                        const response = await api.auth.login({
                            username: loginForm.username.trim(),
                            password: loginForm.password
                        });

                        // 保存认证信息
                        localStorage.setItem('token', response.access_token);
                        localStorage.setItem('userInfo', JSON.stringify(response.user_info));
                        userInfo.value = response.user_info;

                        ElMessage.success('登录成功');
                        currentView.value = 'reports';

                        // 加载初始数据
                        await loadInitialData();

                    } catch (error) {
                        console.error('登录失败:', error);
                    } finally {
                        isLoading.value = false;
                    }
                };

                const handleLogout = async () => {
                    try {
                        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        try {
                            await api.auth.logout();
                        } catch (error) {
                            console.error('登出接口调用失败:', error);
                        }

                        // 清除本地状态
                        localStorage.removeItem('token');
                        localStorage.removeItem('userInfo');
                        userInfo.value = null;
                        currentView.value = 'login';

                        ElMessage.success('已退出登录');

                    } catch (error) {
                        // 用户取消操作
                    }
                };

                const fillDemoAccount = (type) => {
                    if (type === 'testuser') {
                        loginForm.username = 'testuser';
                        loginForm.password = 'testpass';
                    } else if (type === 'admin') {
                        loginForm.username = 'admin';
                        loginForm.password = 'admin123';
                    }
                };

                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const navigateTo = (path) => {
                    activeMenu.value = path;
                    currentView.value = path.substring(1); // 移除开头的 '/'
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const getCurrentPageTitle = () => {
                    const route = menuRoutes.find(r => r.path === activeMenu.value);
                    return route ? route.title : '首页';
                };

                const handleUserCommand = (command) => {
                    if (command === 'logout') {
                        handleLogout();
                    }
                };

                // 加载实时报告
                const refreshRealtimeReport = async () => {
                    try {
                        const data = await api.reports.getRealtimeReport();
                        realtimeReport.value = data;
                    } catch (error) {
                        console.error('加载实时报告失败:', error);
                    }
                };

                // 加载月度报告
                const loadMonthlyReport = async () => {
                    if (!selectedMonth.value) return;

                    try {
                        const [year, month] = selectedMonth.value.split('-');
                        const data = await api.reports.getMonthlyReport(parseInt(year), parseInt(month));
                        currentMonthlyReport.value = data;

                        // 渲染图表
                        nextTick(() => {
                            renderMonthlyChart(data);
                        });
                    } catch (error) {
                        console.error('加载月度报告失败:', error);
                    }
                };

                // 导出月度报告
                const exportMonthlyReport = async () => {
                    if (!selectedMonth.value) {
                        ElMessage.warning('请先选择月份');
                        return;
                    }

                    try {
                        const [year, month] = selectedMonth.value.split('-');
                        const blob = await api.reports.exportMonthlyReport(parseInt(year), parseInt(month));

                        // 创建下载链接
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `月度报告_${selectedMonth.value}.xlsx`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        ElMessage.success('导出成功');
                    } catch (error) {
                        console.error('导出失败:', error);
                    }
                };

                // 获取分布详情
                const fetchDistributionDetails = async () => {
                    if (!distributionMonth.value) return;

                    try {
                        distributionLoading.value = true;
                        const [year, month] = distributionMonth.value.split('-');
                        const data = await api.reports.getMonthlyDistributionDetails(parseInt(year), parseInt(month));
                        distributionDetails.value = data;
                    } catch (error) {
                        console.error('加载分布详情失败:', error);
                    } finally {
                        distributionLoading.value = false;
                    }
                };

                // 渲染月度图表
                const renderMonthlyChart = (data) => {
                    const chartDom = document.getElementById('monthlyChart');
                    if (!chartDom) return;

                    const myChart = echarts.init(chartDom);
                    const option = {
                        title: {
                            text: '部门费用分摊'
                        },
                        tooltip: {
                            trigger: 'item'
                        },
                        series: [
                            {
                                name: '分摊金额',
                                type: 'pie',
                                radius: '50%',
                                data: data.department_details ? data.department_details.map(item => ({
                                    value: item.total_amount,
                                    name: item.department_name
                                })) : [],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    myChart.setOption(option);
                };

                // 格式化日期
                const formatDate = (dateStr) => {
                    if (!dateStr) return '-';
                    return dayjs(dateStr).format('YYYY-MM-DD HH:mm');
                };

                // 加载部门列表
                const loadDepartments = async () => {
                    try {
                        departmentsLoading.value = true;
                        const data = await api.departments.getList(departmentSearchForm);
                        departments.value = data.items || data;
                        filteredDepartments.value = departments.value;
                    } catch (error) {
                        console.error('加载部门列表失败:', error);
                    } finally {
                        departmentsLoading.value = false;
                    }
                };

                // 搜索部门
                const searchDepartments = () => {
                    loadDepartments();
                };

                // 重置部门搜索
                const resetDepartmentSearch = () => {
                    Object.assign(departmentSearchForm, {
                        name: '',
                        is_active: null
                    });
                    loadDepartments();
                };

                // 刷新部门
                const refreshDepartments = () => {
                    loadDepartments();
                };

                // 显示创建部门对话框
                const showCreateDepartmentDialog = () => {
                    isDepartmentEdit.value = false;
                    resetDepartmentForm();
                    departmentDialogVisible.value = true;
                };

                // 显示编辑部门对话框
                const showEditDepartmentDialog = (department) => {
                    isDepartmentEdit.value = true;
                    Object.assign(departmentFormData, {
                        id: department.id,
                        name: department.name,
                        description: department.description || '',
                        is_active: department.is_active
                    });
                    departmentDialogVisible.value = true;
                };

                // 重置部门表单
                const resetDepartmentForm = () => {
                    Object.assign(departmentFormData, {
                        name: '',
                        description: '',
                        is_active: true
                    });
                    if (departmentFormRef.value) {
                        departmentFormRef.value.clearValidate();
                    }
                };

                // 提交部门表单
                const handleDepartmentSubmit = async () => {
                    if (!departmentFormRef.value) return;

                    try {
                        await departmentFormRef.value.validate();
                        departmentSubmitLoading.value = true;

                        if (isDepartmentEdit.value) {
                            await api.departments.update(departmentFormData.id, departmentFormData);
                            ElMessage.success('部门更新成功');
                        } else {
                            await api.departments.create(departmentFormData);
                            ElMessage.success('部门创建成功');
                        }

                        departmentDialogVisible.value = false;
                        loadDepartments();
                    } catch (error) {
                        console.error('提交部门表单失败:', error);
                    } finally {
                        departmentSubmitLoading.value = false;
                    }
                };

                // 删除部门
                const deleteDepartment = async (department) => {
                    try {
                        await ElMessageBox.confirm(`确定要删除部门"${department.name}"吗？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        await api.departments.delete(department.id);
                        ElMessage.success('删除成功');
                        loadDepartments();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除部门失败:', error);
                        }
                    }
                };

                // 加载初始数据
                // 加载宿舍列表
                const loadDormitories = async () => {
                    try {
                        dormitoriesLoading.value = true;
                        const data = await api.dormitories.getList(dormitorySearchForm);
                        dormitories.value = data.items || data;
                        filteredDormitories.value = dormitories.value;
                    } catch (error) {
                        console.error('加载宿舍列表失败:', error);
                    } finally {
                        dormitoriesLoading.value = false;
                    }
                };

                // 搜索宿舍
                const searchDormitories = () => {
                    loadDormitories();
                };

                // 重置宿舍搜索
                const resetDormitorySearch = () => {
                    Object.assign(dormitorySearchForm, {
                        name: '',
                        is_active: null
                    });
                    loadDormitories();
                };

                // 刷新宿舍
                const refreshDormitories = () => {
                    loadDormitories();
                };

                // 获取入住率
                const getOccupancyRate = (dormitory) => {
                    if (!dormitory.total_beds || dormitory.total_beds === 0) return 0;
                    const occupied = dormitory.occupied_beds || 0;
                    return Math.round((occupied / dormitory.total_beds) * 100);
                };

                // 获取进度条颜色
                const getProgressColor = (percentage) => {
                    if (percentage >= 90) return '#f56c6c';
                    if (percentage >= 70) return '#e6a23c';
                    if (percentage >= 50) return '#409eff';
                    return '#67c23a';
                };

                // 显示创建宿舍对话框
                const showCreateDormitoryDialog = () => {
                    isDormitoryEdit.value = false;
                    resetDormitoryForm();
                    dormitoryDialogVisible.value = true;
                };

                // 显示编辑宿舍对话框
                const showEditDormitoryDialog = (dormitory) => {
                    isDormitoryEdit.value = true;
                    Object.assign(dormitoryFormData, {
                        id: dormitory.id,
                        name: dormitory.name,
                        department_id: dormitory.department_id,
                        total_beds: dormitory.total_beds,
                        description: dormitory.description || '',
                        is_active: dormitory.is_active
                    });
                    dormitoryDialogVisible.value = true;
                };

                // 查看宿舍详情
                const viewDormitoryDetails = (dormitory) => {
                    ElMessage.info('查看宿舍详情功能开发中...');
                };

                // 重置宿舍表单
                const resetDormitoryForm = () => {
                    Object.assign(dormitoryFormData, {
                        name: '',
                        department_id: '',
                        total_beds: 1,
                        description: '',
                        is_active: true
                    });
                    if (dormitoryFormRef.value) {
                        dormitoryFormRef.value.clearValidate();
                    }
                };

                // 提交宿舍表单
                const handleDormitorySubmit = async () => {
                    if (!dormitoryFormRef.value) return;

                    try {
                        await dormitoryFormRef.value.validate();
                        dormitorySubmitLoading.value = true;

                        if (isDormitoryEdit.value) {
                            await api.dormitories.update(dormitoryFormData.id, dormitoryFormData);
                            ElMessage.success('宿舍更新成功');
                        } else {
                            await api.dormitories.create(dormitoryFormData);
                            ElMessage.success('宿舍创建成功');
                        }

                        dormitoryDialogVisible.value = false;
                        loadDormitories();
                    } catch (error) {
                        console.error('提交宿舍表单失败:', error);
                    } finally {
                        dormitorySubmitLoading.value = false;
                    }
                };

                // 删除宿舍
                const deleteDormitory = async (dormitory) => {
                    try {
                        await ElMessageBox.confirm(`确定要删除宿舍"${dormitory.name}"吗？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        await api.dormitories.delete(dormitory.id);
                        ElMessage.success('删除成功');
                        loadDormitories();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除宿舍失败:', error);
                        }
                    }
                };

                // 加载住户列表
                const loadResidents = async () => {
                    try {
                        residentsLoading.value = true;
                        const data = await api.residents.getList(residentSearchForm);
                        residents.value = data.items || data;
                        filteredResidents.value = residents.value;
                    } catch (error) {
                        console.error('加载住户列表失败:', error);
                    } finally {
                        residentsLoading.value = false;
                    }
                };

                // 搜索住户
                const searchResidents = () => {
                    loadResidents();
                };

                // 重置住户搜索
                const resetResidentSearch = () => {
                    Object.assign(residentSearchForm, {
                        keyword: '',
                        department_id: '',
                        is_active: null
                    });
                    loadResidents();
                };

                // 刷新住户
                const refreshResidents = () => {
                    loadResidents();
                };

                // 显示创建住户对话框
                const showCreateResidentDialog = () => {
                    isResidentEdit.value = false;
                    resetResidentForm();
                    residentDialogVisible.value = true;
                };

                // 显示编辑住户对话框
                const showEditResidentDialog = (resident) => {
                    isResidentEdit.value = true;
                    Object.assign(residentFormData, {
                        id: resident.id,
                        name: resident.name,
                        employee_id: resident.employee_id || '',
                        phone: resident.phone || '',
                        email: resident.email || '',
                        department_id: resident.department_id,
                        gender: resident.gender || 'MALE',
                        is_active: resident.is_active
                    });
                    residentDialogVisible.value = true;
                };

                // 重置住户表单
                const resetResidentForm = () => {
                    Object.assign(residentFormData, {
                        name: '',
                        employee_id: '',
                        phone: '',
                        email: '',
                        department_id: '',
                        gender: 'MALE',
                        is_active: true
                    });
                    if (residentFormRef.value) {
                        residentFormRef.value.clearValidate();
                    }
                };

                // 提交住户表单
                const handleResidentSubmit = async () => {
                    if (!residentFormRef.value) return;

                    try {
                        await residentFormRef.value.validate();
                        residentSubmitLoading.value = true;

                        if (isResidentEdit.value) {
                            await api.residents.update(residentFormData.id, residentFormData);
                            ElMessage.success('住户更新成功');
                        } else {
                            await api.residents.create(residentFormData);
                            ElMessage.success('住户创建成功');
                        }

                        residentDialogVisible.value = false;
                        loadResidents();
                    } catch (error) {
                        console.error('提交住户表单失败:', error);
                    } finally {
                        residentSubmitLoading.value = false;
                    }
                };

                // 删除住户
                const deleteResident = async (resident) => {
                    try {
                        await ElMessageBox.confirm(`确定要删除住户"${resident.name}"吗？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        await api.residents.delete(resident.id);
                        ElMessage.success('删除成功');
                        loadResidents();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除住户失败:', error);
                        }
                    }
                };

                // 加载记录列表
                const loadRecords = async () => {
                    try {
                        recordsLoading.value = true;
                        const params = { ...recordSearchForm };

                        // 添加日期范围参数
                        if (recordDateRange.value && recordDateRange.value.length === 2) {
                            params.start_date = recordDateRange.value[0];
                            params.end_date = recordDateRange.value[1];
                        }

                        // 添加分页参数
                        params.page = recordPagination.page;
                        params.size = recordPagination.size;

                        const data = await api.records.getList(params);
                        records.value = data.items || data;
                        filteredRecords.value = records.value;

                        // 更新分页信息
                        if (data.total !== undefined) {
                            recordPagination.total = data.total;
                        }
                    } catch (error) {
                        console.error('加载记录列表失败:', error);
                    } finally {
                        recordsLoading.value = false;
                    }
                };

                // 搜索记录
                const searchRecords = () => {
                    loadRecords();
                };

                // 重置记录搜索
                const resetRecordSearch = () => {
                    Object.assign(recordSearchForm, {
                        keyword: '',
                        status: '',
                        dormitory_id: '',
                        department_id: ''
                    });
                    recordDateRange.value = [];
                    recordPagination.page = 1;
                    loadRecords();
                };

                // 刷新记录
                const refreshRecords = () => {
                    loadRecords();
                };

                // 显示活跃记录
                const showActiveRecords = () => {
                    recordSearchForm.status = 'ACTIVE';
                    loadRecords();
                };

                // 查看记录详情
                const viewRecord = (record) => {
                    ElMessage.info('查看记录功能开发中...');
                };

                // 离开记录
                const checkOutRecord = async (record) => {
                    try {
                        await ElMessageBox.confirm(`确定要为"${record.resident_name}"办理离开手续吗？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        // 这里应该调用离开接口
                        ElMessage.success('离开手续办理成功');
                        loadRecords();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('办理离开手续失败:', error);
                        }
                    }
                };

                // 获取记录状态类型
                const getRecordStatusType = (status) => {
                    switch (status) {
                        case 'ACTIVE': return 'success';
                        case 'COMPLETED': return 'info';
                        case 'CANCELLED': return 'warning';
                        default: return 'info';
                    }
                };

                // 获取记录状态文本
                const getRecordStatusText = (status) => {
                    switch (status) {
                        case 'ACTIVE': return '入住中';
                        case 'COMPLETED': return '已离开';
                        case 'CANCELLED': return '已取消';
                        default: return '未知';
                    }
                };

                // 分页处理
                const handleRecordSizeChange = (size) => {
                    recordPagination.size = size;
                    recordPagination.page = 1;
                    loadRecords();
                };

                const handleRecordPageChange = (page) => {
                    recordPagination.page = page;
                    loadRecords();
                };

                // 显示创建记录对话框
                const showCreateRecordDialog = () => {
                    ElMessage.info('创建记录功能开发中...');
                };

                // 编辑记录
                const editRecord = (record) => {
                    ElMessage.info('编辑记录功能开发中...');
                };

                // 删除记录
                const deleteRecord = async (record) => {
                    try {
                        await ElMessageBox.confirm(`确定要删除这条记录吗？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        await api.records.delete(record.id);
                        ElMessage.success('删除成功');
                        loadRecords();
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除记录失败:', error);
                        }
                    }
                };

                const loadInitialData = async () => {
                    await Promise.all([
                        refreshRealtimeReport(),
                        loadDepartments(),
                        loadDormitories(),
                        loadResidents(),
                        loadRecords(),
                        loadMonthlyReport(),
                        fetchDistributionDetails()
                    ]);
                };

                // 检查认证状态
                const checkAuth = async () => {
                    const token = localStorage.getItem('token');
                    if (!token) {
                        currentView.value = 'login';
                        return;
                    }

                    try {
                        await api.auth.verifyToken();
                        const savedUserInfo = localStorage.getItem('userInfo');
                        if (savedUserInfo) {
                            userInfo.value = JSON.parse(savedUserInfo);
                        }
                        currentView.value = 'reports';
                        await loadInitialData();
                    } catch (error) {
                        console.error('认证检查失败:', error);
                        localStorage.removeItem('token');
                        localStorage.removeItem('userInfo');
                        currentView.value = 'login';
                    }
                };

                // 组件挂载时检查认证状态
                onMounted(() => {
                    checkAuth();
                });

                return {
                    // 状态
                    currentView,
                    isCollapsed,
                    activeMenu,
                    isLoading,
                    userInfo,
                    loginForm,
                    loginRules,
                    realtimeReport,
                    monthlyReports,
                    yearlySummary,
                    selectedMonth,
                    currentMonthlyReport,
                    distributionMonth,
                    distributionDetails,
                    distributionLoading,
                    departments,
                    dormitories,
                    residents,
                    records,

                    // 筛选后的数据
                    filteredDepartments,
                    filteredDormitories,
                    filteredResidents,
                    filteredRecords,
                    recordDateRange,
                    recordPagination,

                    // 对话框状态
                    departmentDialogVisible,
                    isDepartmentEdit,
                    departmentSubmitLoading,
                    departmentFormRef,
                    dormitoryDialogVisible,
                    isDormitoryEdit,
                    dormitorySubmitLoading,
                    dormitoryFormRef,
                    residentDialogVisible,
                    isResidentEdit,
                    residentSubmitLoading,
                    residentFormRef,

                    // 表单数据
                    departmentFormData,
                    dormitoryFormData,
                    residentFormData,
                    departmentFormRules,
                    dormitoryFormRules,
                    residentFormRules,

                    menuRoutes,

                    // 加载状态
                    departmentsLoading,
                    dormitoriesLoading,
                    residentsLoading,
                    recordsLoading,

                    // 搜索表单
                    departmentSearchForm,
                    dormitorySearchForm,
                    residentSearchForm,
                    recordSearchForm,

                    // 计算属性
                    isAuthenticated,

                    // 方法
                    handleLogin,
                    handleLogout,
                    fillDemoAccount,
                    toggleSidebar,
                    navigateTo,
                    refreshPage,
                    getCurrentPageTitle,
                    handleUserCommand,
                    formatDate,
                    refreshRealtimeReport,
                    loadMonthlyReport,
                    exportMonthlyReport,
                    fetchDistributionDetails,
                    renderMonthlyChart,

                    // 部门相关方法
                    loadDepartments,
                    searchDepartments,
                    resetDepartmentSearch,
                    refreshDepartments,
                    showCreateDepartmentDialog,
                    showEditDepartmentDialog,
                    resetDepartmentForm,
                    handleDepartmentSubmit,
                    deleteDepartment,

                    // 宿舍相关方法
                    loadDormitories,
                    searchDormitories,
                    resetDormitorySearch,
                    refreshDormitories,
                    getOccupancyRate,
                    getProgressColor,
                    showCreateDormitoryDialog,
                    showEditDormitoryDialog,
                    viewDormitoryDetails,
                    resetDormitoryForm,
                    handleDormitorySubmit,
                    deleteDormitory,

                    // 住户相关方法
                    loadResidents,
                    searchResidents,
                    resetResidentSearch,
                    refreshResidents,
                    showCreateResidentDialog,
                    showEditResidentDialog,
                    resetResidentForm,
                    handleResidentSubmit,
                    deleteResident,
                    loadRecords,
                    searchRecords,
                    resetRecordSearch,
                    refreshRecords,
                    showActiveRecords,
                    showCreateRecordDialog,
                    viewRecord,
                    editRecord,
                    checkOutRecord,
                    deleteRecord,
                    getRecordStatusType,
                    getRecordStatusText,
                    handleRecordSizeChange,
                    handleRecordPageChange,
                    loadInitialData,
                    checkAuth
                };
            }
        });

        // 注册 Element Plus 组件和图标
        app.use(ElementPlus);

        // 注册图标组件
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
