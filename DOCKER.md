# 🐳 Docker 部署指南

本文档详细介绍如何使用 Docker 部署宿舍入住管理系统。

## 📋 目录

- [快速开始](#快速开始)
- [开发环境](#开发环境)
- [生产环境](#生产环境)
- [配置说明](#配置说明)
- [常用命令](#常用命令)
- [故障排除](#故障排除)

## 🚀 快速开始

### 最简单的启动方式

```bash
# 克隆项目
git clone <repository-url>
cd BedSharingCalc

# 使用默认配置启动
docker-compose up -d

# 访问应用
# 前端: http://localhost
# 后端API: http://localhost:8000
```

## 🛠️ 开发环境

### 启动开发环境

```bash
# 启动开发环境（包含热重载、数据库管理工具等）
make docker-dev

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

### 开发环境服务

| 服务 | 端口 | 描述 |
|------|------|------|
| 前端 | 3000 | Vue.js 开发服务器 |
| 后端 | 8000 | FastAPI 开发服务器 |
| PostgreSQL | 5432 | 数据库服务 |
| Redis | 6379 | 缓存服务 |
| Adminer | 8080 | 数据库管理工具 |

### 开发环境特性

- ✅ **热重载**：代码修改自动重启
- ✅ **数据库管理**：Adminer 可视化管理
- ✅ **日志挂载**：实时查看应用日志
- ✅ **代码挂载**：本地修改实时生效

## 🏭 生产环境

### 环境准备

1. **创建生产环境配置**
```bash
# 复制环境变量模板
cp .env.prod.example .env.prod

# 编辑配置文件
vim .env.prod
```

2. **必须修改的配置项**
```env
# 安全密钥 - 必须修改
SECRET_KEY=your-super-secure-secret-key

# 数据库密码
POSTGRES_PASSWORD=your-secure-postgres-password

# Redis密码
REDIS_PASSWORD=your-secure-redis-password

# 域名配置
DOMAIN=yourdomain.com
```

### 启动生产环境

```bash
# 启动生产环境
make docker-prod

# 或者直接使用 docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

### 生产环境特性

- ✅ **多阶段构建**：优化镜像大小
- ✅ **非root用户**：增强安全性
- ✅ **健康检查**：自动故障恢复
- ✅ **资源限制**：防止资源滥用
- ✅ **数据持久化**：数据安全保障
- ✅ **自动备份**：定时数据备份

## ⚙️ 配置说明

### Docker Compose 文件

| 文件 | 用途 | 特点 |
|------|------|------|
| `docker-compose.yml` | 基础配置 | SQLite + 基本功能 |
| `docker-compose.dev.yml` | 开发环境 | 热重载 + 开发工具 |
| `docker-compose.prod.yml` | 生产环境 | PostgreSQL + 安全配置 |

### Dockerfile 文件

| 文件 | 用途 | 特点 |
|------|------|------|
| `Dockerfile` | 后端生产镜像 | 多阶段构建 + 安全优化 |
| `Dockerfile.dev` | 后端开发镜像 | 开发工具 + 调试支持 |
| `frontend/Dockerfile` | 前端生产镜像 | Nginx + 静态文件 |
| `frontend/Dockerfile.dev` | 前端开发镜像 | 热重载 + 开发服务器 |

### 数据卷说明

```yaml
volumes:
  postgres_data:     # PostgreSQL 数据
  redis_data:        # Redis 数据
  backend_logs:      # 后端日志
  backend_exports:   # 导出文件
```

## 🔧 常用命令

### 基本操作

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f [service_name]

# 查看服务状态
docker-compose ps

# 重启服务
docker-compose restart [service_name]
```

### 开发操作

```bash
# 进入容器
docker-compose exec backend bash
docker-compose exec frontend sh

# 查看实时日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 重新构建镜像
docker-compose build --no-cache

# 更新依赖后重启
docker-compose down
docker-compose up -d --build
```

### 数据库操作

```bash
# 连接数据库
docker-compose exec postgres psql -U bedsharing -d bedsharing

# 备份数据库
docker-compose exec postgres pg_dump -U bedsharing bedsharing > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U bedsharing bedsharing < backup.sql

# 查看数据库日志
docker-compose logs postgres
```

### 清理操作

```bash
# 停止并删除容器
docker-compose down -v

# 清理未使用的镜像
docker image prune -f

# 清理所有未使用的资源
docker system prune -a -f

# 使用 Makefile 清理
make docker-clean
```

## 🔍 监控和调试

### 健康检查

```bash
# 检查所有服务健康状态
docker-compose ps

# 查看特定服务健康状态
docker inspect --format='{{.State.Health.Status}}' bedsharing-backend
```

### 性能监控

```bash
# 查看资源使用情况
docker stats

# 查看特定容器资源使用
docker stats bedsharing-backend bedsharing-frontend
```

### 日志分析

```bash
# 查看错误日志
docker-compose logs --tail=100 backend | grep ERROR

# 实时监控日志
docker-compose logs -f --tail=0 backend

# 导出日志到文件
docker-compose logs backend > backend.log
```

## 🚨 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8000

# 修改 docker-compose.yml 中的端口映射
ports:
  - "8001:8000"  # 改为其他端口
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres

# 重启数据库服务
docker-compose restart postgres
```

#### 3. 前端无法访问后端
```bash
# 检查网络连接
docker network ls
docker network inspect bedsharing_bedsharing-network

# 检查后端健康状态
curl http://localhost:8000/health
```

#### 4. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs [service_name]

# 检查配置文件语法
docker-compose config

# 重新构建镜像
docker-compose build --no-cache [service_name]
```

### 性能优化

#### 1. 镜像优化
```bash
# 查看镜像大小
docker images | grep bedsharing

# 清理构建缓存
docker builder prune -f
```

#### 2. 资源限制
```yaml
# 在 docker-compose.yml 中添加资源限制
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

## 📚 最佳实践

### 安全建议

1. **使用非root用户**：所有容器都使用非root用户运行
2. **定期更新镜像**：保持基础镜像最新
3. **限制网络访问**：只暴露必要的端口
4. **使用secrets管理**：敏感信息使用Docker secrets
5. **定期备份数据**：设置自动备份策略

### 生产部署建议

1. **使用反向代理**：Nginx 或 Traefik
2. **配置SSL证书**：Let's Encrypt 自动续期
3. **设置监控告警**：Prometheus + Grafana
4. **日志聚合**：ELK Stack 或 Loki
5. **自动化部署**：CI/CD 流水线

---

**更多信息请参考**：
- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [项目 README](README.md)
