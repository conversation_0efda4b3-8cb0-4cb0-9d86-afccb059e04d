# 月度报告Excel格式设计文档

## 1. 需求分析

### 1.1 当前状态
- 现有的Excel导出功能采用简单的表格格式
- 数据按宿舍-部门层级展示
- 缺乏视觉层次和美观性

### 1.2 目标格式分析
根据提供的图片，新的Excel格式需要实现：

1. **宿舍信息区域**（左侧）
   - 宿舍名称（如"申江宿舍"）
   - 采用合并单元格显示

2. **人员信息表格**（右侧）
   - 序号列
   - 姓名列
   - 部门列
   - 项目组列

3. **统计信息显示**（右侧）
   - 部门占比统计（如"财资产品部62.5%"）
   - 动态计算并显示

4. **视觉效果**
   - 不同宿舍使用不同背景色
   - 表格边框和对齐
   - 合并单元格布局

## 2. 数据模型扩展




### 2.2 数据结构调整
导出数据需要按以下结构组织：
```python
{
    "dormitories": [
        {
            "dormitory_name": "申江宿舍",
            "departments": [
                {
                    "department_name": "财资产品部",
                    "percentage": 62.5,
                    "residents": [
                        {
                            "name": "李波",
                            "department": "财资产品部", 
                            "project_group": "信贷理财"
                        }
                    ]
                }
            ]
        }
    ]
}
```

## 3. Excel布局设计

### 3.1 整体布局
- 每个宿舍占用一个独立的表格区域
- 采用横向布局：左侧宿舍信息，右侧人员表格

### 3.2 单个宿舍布局结构
```
+------------------+--+------+--------+--------+--------+
|                  |  | 序号 |  姓名  |  部门  |  项目组  |
|   宿舍名称       |  +------+--------+--------+--------+
| (宿舍地址)       |  |  1   |  李波  |财资产品部|信贷理财|
|                  |  |  2   |  张三  |财资产品部|信贷理财|
|                  |  |  3   |  ...   |  ...   |  ...   |
+------------------+--+------+--------+--------+--------+
```

### 3.4 颜色方案
- 宿舍1：浅蓝色背景 (#E6F3FF)
- 宿舍2：浅绿色背景 (#E6FFE6)
- 宿舍3：浅黄色背景 (#FFFACD)
- 宿舍4：浅粉色背景 (#FFE6F0)
- 循环使用颜色
